{"name": "sspx-seminary-registry", "private": true, "type": "module", "scripts": {"build": "react-router build", "migrate": "npx tsx scripts/migrate.ts", "db:generate": "dotenv -- drizzle-kit generate", "db:migrate": "dotenv -- drizzle-kit migrate", "db:push": "dotenv -- drizzle-kit push", "dev": "dotenv -- node server.js", "start": "node server.js", "typecheck": "react-router typegen && tsc -b"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@hookform/resolvers": "^5.0.1", "@openfga/sdk": "^0.9.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.0", "@react-router/express": "^7.6.1", "@react-router/fs-routes": "^7.6.1", "@react-router/node": "^7.6.1", "@tanstack/react-table": "^8.21.3", "better-sqlite3": "^11.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.7.5", "date-fns": "^4.1.0", "drizzle-orm": "^0.41.0", "drizzle-zod": "^0.8.2", "express": "^4.21.1", "isbot": "^5.1.17", "lucide-react": "^0.487.0", "morgan": "^1.10.0", "postgres": "^3.4.5", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-router": "^7.6.1", "recharts": "^2.15.2", "remix-hook-form": "^7.0.0", "remix-utils": "^8.7.0", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@react-router/dev": "^7.5.0", "@tailwindcss/vite": "^4.1.3", "@types/better-sqlite3": "^7.6.13", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/express-serve-static-core": "^5.0.1", "@types/morgan": "^1.9.9", "@types/node": "^20", "@types/pg": "^8.11.10", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "dotenv-cli": "^7.4.3", "drizzle-kit": "^0.28.1", "tailwindcss": "^4.1.3", "tsx": "^4.19.2", "typescript": "^5.7.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}, "overrides": {"react-is": "^19.1.0"}}
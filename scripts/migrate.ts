// scripts/migrate.ts
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { InferInsertModel, InferSelectModel } from 'drizzle-orm';
import path from 'path';

// Assuming your schema is in ../database/schema relative to this script
import * as schema from '../database/schema';
import { hashPassword } from '../app/lib/utils';

// --- Configuration ---
const OLD_DB_PATH = process.env.OLD_DB_PATH || './old_registry.db'; // Path to your original SQLite DB
const NEW_DB_PATH = process.env.NEW_DB_PATH || './new_registry.db'; // Path for the new SQLite DB
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD_HASH = await hashPassword('password');

// --- Types ---
type OldSeminarian = InferSelectModel<typeof schema.seminarians> & { OldID: number }; // Add OldID if needed elsewhere

// --- Helper Functions ---
function dateStringToDate(dateStr: string | null | undefined): Date | null {
    if (!dateStr) return null;
    let date = new Date(dateStr);
    if (!isNaN(date.getTime())) return date;
    date = new Date(dateStr.replace(' ', 'T'));
    if (!isNaN(date.getTime())) return date;
    console.warn(`Could not parse date: ${dateStr}`);
    return null;
}

function valueToBoolean(boolVal: any): boolean | null {
    if (boolVal === null || boolVal === undefined) return null;
    return Boolean(boolVal);
}



// --- Main Migration Logic ---
async function runMigration() {
    console.log(`Connecting to old database: ${path.resolve(OLD_DB_PATH)}`);
    const dbSource = new Database(OLD_DB_PATH); // Removed verbose logging

    console.log(`Connecting to new database: ${path.resolve(NEW_DB_PATH)}`);
    const dbTargetSqlite = new Database(NEW_DB_PATH); // Removed verbose logging
    const dbTarget = drizzle(dbTargetSqlite, { schema });

    console.log('Starting data migration...');

    try {
        // Use Drizzle's transaction function
        await dbTarget.transaction(async (tx) => {
            console.log('Beginning migration transaction...');

            // --- 1. Load Mappings ---
            console.log('Loading ID mappings...');
            const statusCodes = dbSource.prepare('SELECT ID, Description FROM StatusCodes').all() as { ID: number, Description: string }[];
            const statusCodeMap = new Map(statusCodes.map(s => [s.ID, s.Description]));

            const yearNames = dbSource.prepare('SELECT ID, Description FROM YearNames').all() as { ID: number, Description: string }[];
            const yearNameMap = new Map(yearNames.map(y => [y.ID, y.Description]));

            const orderNames = dbSource.prepare('SELECT ID, Name, LatinName FROM OrderNames').all() as { ID: number, Name: string, LatinName: string }[];
            const orderNameMap = new Map(orderNames.map(o => [o.ID, o.Name]));

            // --- 2. Migrate Seminarians ---
            console.log('Migrating Seminarians...');
            const oldSeminariansRaw = dbSource.prepare('SELECT * FROM Seminarians').all() as any[];
            const seminarianIdMap = new Map<number, number>(); // Map<oldId, newId>

            for (const oldSem of oldSeminariansRaw) {
                const statusDesc = statusCodeMap.get(oldSem.StatusCode);
                const mappedStatus = schema.statusEnumValues.find(e => e === statusDesc) || 'Applicant';

                // Map CurrentYear to seminaryYear
                const yearDesc = oldSem.CurrentYear !== null ? yearNameMap.get(oldSem.CurrentYear) : undefined;
                const mappedSeminaryYear = schema.seminaryYearEnumValues.find(e => e === yearDesc);

                const isValidEmail = oldSem.Email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(oldSem.Email);

                const semToInsert: InferInsertModel<typeof schema.seminarians> = {
                    lastName: oldSem.LastName,
                    firstName: oldSem.FirstName,
                    middleName: oldSem.MiddleName,
                    street1: oldSem.Street1,
                    street2: oldSem.Street2,
                    city: oldSem.City,
                    state: oldSem.State,
                    zip: oldSem.ZIP,
                    country: oldSem.Country,
                    mailingStreet: oldSem.MailingStreet,
                    phone: oldSem.Phone,
                    email: isValidEmail ? oldSem.Email : null,
                    citizenship: oldSem.Citizenship,
                    diocese: oldSem.Diocese,
                    dob: dateStringToDate(oldSem.DOB),
                    birthCity: oldSem.BirthCity,
                    birthState: oldSem.BirthState,
                    birthCountry: oldSem.BirthCountry,
                    status: mappedStatus,
                    seminaryYear: mappedSeminaryYear,
                    roomNumber: oldSem.RoomNum,
                    notes: oldSem.Notes,
                };

                const [newSem] = await tx.insert(schema.seminarians).values(semToInsert).returning({ id: schema.seminarians.id });
                seminarianIdMap.set(oldSem.ID, newSem.id);
            }
            console.log(`  Migrated ${seminarianIdMap.size} seminarians.`);

            // --- 3. Migrate Personnel/Users ---
            console.log('Migrating Users...');
            const professorMap = new Map<string, number>(); // Map<professorName, newUserId>

            // Get unique professors from old grades tables
            const professors = dbSource.prepare(`
                SELECT DISTINCT Professor FROM CurrentGrades WHERE Professor IS NOT NULL AND Professor != ''
                UNION
                SELECT DISTINCT Professor FROM HistoricalGrades WHERE Professor IS NOT NULL AND Professor != ''
            `).all() as { Professor: string }[];

            for (const prof of professors) {
                console.log(`  Processing Professor: ${prof.Professor}`);
                const username = prof.Professor.toLowerCase().replace(/\s+/g, '');

                const userToInsert: InferInsertModel<typeof schema.users> = {
                    firstName: prof.Professor, // Assuming name is just stored like this
                    lastName: '',
                    username: username,
                    passwordHash: ADMIN_PASSWORD_HASH,
                    isActive: true,
                };

                const [newUser] = await tx.insert(schema.users).values(userToInsert).returning({ id: schema.users.id });
                professorMap.set(prof.Professor, newUser.id);
                console.log(`    -> Created user ID: ${newUser.id}`);
            }
            console.log(` Migrated ${professorMap.size} professor users.`);

            // Create default admin user
            console.log('Creating default admin user...');
            const [adminUser] = await tx.insert(schema.users).values({
                username: ADMIN_USERNAME,
                passwordHash: ADMIN_PASSWORD_HASH,
                firstName: 'Admin',
                lastName: 'User',
                isActive: true,
            }).returning({ id: schema.users.id });
            console.log(`  -> Created admin user ID: ${adminUser.id}`);

            // --- 4. Migrate Departments ---
            console.log('Migrating Departments...');
            const oldDepartments = dbSource.prepare('SELECT * FROM Departments').all() as any[];
            const departmentMap = new Map<number, number>(); // Map<oldDeptId, newDeptId>
            for (const oldDept of oldDepartments) {
                const category = schema.departmentCategoryEnumValues.find(c => c === oldDept.Category) || 'General'; // Default category

                // Find the sub-prefect seminarian ID (using old SemID referenced by Prefect column)
                let subPrefectSeminarianId: number | null = null;
                if (oldDept.Prefect) {
                    const prefectSemId = seminarianIdMap.get(oldDept.Prefect);
                    if (prefectSemId) {
                        subPrefectSeminarianId = prefectSemId;
                    } else {
                        console.warn(`  Could not map old Prefect ID ${oldDept.Prefect} to new Seminarian ID for Dept "${oldDept.DeptName}"`);
                    }
                }

                const [newDept] = await tx.insert(schema.departments).values({
                    name: oldDept.DeptName,
                    category: category,
                    subPrefectSeminarianId: subPrefectSeminarianId,
                }).returning({ id: schema.departments.id });
                departmentMap.set(oldDept.ID, newDept.id);
            }
            console.log(`  Migrated ${departmentMap.size} departments.`);

            // --- 5. Migrate Department Assignments ---
            console.log('Migrating Department Assignments...');
            const oldAssignments = dbSource.prepare('SELECT * FROM DepartmentAssignments').all() as any[];
            let assignmentCount = 0;
            for (const oldAssign of oldAssignments) {
                const newSemId = seminarianIdMap.get(oldAssign.SemID);
                const newDeptId = departmentMap.get(oldAssign.DeptID);

                if (!newSemId) {
                    console.warn(`  Skipping assignment for unknown old SemID: ${oldAssign.SemID}, DeptID: ${oldAssign.DeptID}`);
                    continue;
                }
                if (!newDeptId) {
                    console.warn(`  Skipping assignment for unknown old DeptID: ${oldAssign.DeptID}, SemID: ${oldAssign.SemID}`);
                    continue;
                }

                // Insert ignore duplicate PK errors if any (though shouldn't happen with clean target)
                try {
                    await tx.insert(schema.departmentAssignments).values({
                        seminarianId: newSemId,
                        departmentId: newDeptId,
                    });
                    assignmentCount++;
                } catch (e: any) {
                    if (e.code === 'SQLITE_CONSTRAINT_PRIMARYKEY') {
                        console.warn(`  Duplicate assignment skipped: SemID=${newSemId}, DeptID=${newDeptId}`);
                    } else {
                        throw e; // Re-throw other errors
                    }
                }
            }
            console.log(`  Migrated ${assignmentCount} department assignments.`);

            // --- 6. Migrate Applications (formerly Document Files) ---
            console.log('Migrating Applications...');
            const oldDocs = dbSource.prepare('SELECT * FROM DocumentFile').all() as any[];
            let appCount = 0;
            for (const oldDoc of oldDocs) {
                const newSemId = seminarianIdMap.get(oldDoc.SemID);
                if (!newSemId) {
                    console.warn(`  Skipping application for unknown old SemID: ${oldDoc.SemID}`);
                    continue;
                }
                const appToInsert: InferInsertModel<typeof schema.applications> = {
                    seminarianId: newSemId,
                    applicationEntered: dateStringToDate(oldDoc.ApplicationEntered),
                    acceptanceSent: dateStringToDate(oldDoc.AcceptanceSent),
                    baptismalCertificate: valueToBoolean(oldDoc.BaptismalCertificate),
                    confirmationCertificate: valueToBoolean(oldDoc.ConfirmationCertificate),
                    catholicMarriage: valueToBoolean(oldDoc.CatholicMarriage),
                    marriageCertificate: valueToBoolean(oldDoc.MarriageCertificate),
                    academicTranscripts: valueToBoolean(oldDoc.AcademicTranscripts),
                    referenceLetter: valueToBoolean(oldDoc.ReferenceLetter),
                    referenceLetterFrom: oldDoc.ReferenceLetterFrom,
                    applicationNotes: oldDoc.ApplicationNotes,
                    sspxSchool: oldDoc.SSPXSchool,
                    perpetualEngagement: dateStringToDate(oldDoc.PerpetualEngagement),
                    skills: oldDoc.Skills,
                    musicalAbility: oldDoc.MusicalAbility,
                    notes: oldDoc.OtherNotes, // Renamed column
                };
                await tx.insert(schema.applications).values(appToInsert);
                appCount++;
            }
            console.log(`  Migrated ${appCount} application records.`);

            // --- 7. Skip Enrollments & Status Updates (not in current schema) ---
            console.log('Skipping Enrollments and Status Updates (tables not in current schema)...');

            // --- 9. Migrate Courses ---
            console.log('Migrating Courses...');
            const oldCourses = dbSource.prepare(`
                SELECT DISTINCT CourseName, MAX(credits) as Credits
                FROM (
                    SELECT CourseName, Sem1_PotentialCredits as credits FROM CurrentGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                    UNION ALL
                    SELECT CourseName, Sem2_PotentialCredits as credits FROM CurrentGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                    UNION ALL
                    SELECT CourseName, Sem1_PotentialCredits as credits FROM HistoricalGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                    UNION ALL
                    SELECT CourseName, Sem2_PotentialCredits as credits FROM HistoricalGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                )
                WHERE credits IS NOT NULL
                GROUP BY CourseName
            `).all() as { CourseName: string, Credits: number }[];
            const courseMap = new Map<string, number>(); // Map<courseName, newCourseId>
            for(const oldC of oldCourses) {
                const [newCourse] = await tx.insert(schema.courses).values({
                    courseName: oldC.CourseName,
                    credits: oldC.Credits || 0, // Default credits to 0 if null
                }).returning({ id: schema.courses.id });
                courseMap.set(oldC.CourseName, newCourse.id);
            }
            console.log(`  Migrated ${courseMap.size} unique courses.`);

            // --- Determine Current Academic Year ---
            console.log('Determining current academic year from old enrollments...');
            let currentAcademicYear: number;
            try {
                const maxYearResult = dbSource.prepare('SELECT MAX(YearDateID) as MaxYear FROM Enrollments WHERE YearDateID IS NOT NULL').get() as { MaxYear: number | null };
                if (maxYearResult && maxYearResult.MaxYear !== null) {
                    // Assuming YearDateID is like 23, convert to 2023. Handles years YY >= 70 as 19YY, otherwise 20YY.
                    currentAcademicYear = maxYearResult.MaxYear >= 70 ? 1900 + maxYearResult.MaxYear : 2000 + maxYearResult.MaxYear;
                    console.log(`  Determined current academic year as: ${currentAcademicYear}`);
                } else {
                    console.warn('Could not determine current academic year from enrollments, falling back to current calendar year.');
                    currentAcademicYear = new Date().getFullYear();
                }
            } catch (err) {
                 console.error('Error querying max YearDateID from old Enrollments:', err);
                 console.warn('Falling back to current calendar year.');
                 currentAcademicYear = new Date().getFullYear();
            }

            // --- 10. Migrate Course Enrollments & Course Offerings (Combined Logic) ---
            console.log('Migrating Course Enrollments and creating Course Offerings...');
            const courseOfferingCache = new Map<string, number>(); // Key: "courseId-acadYear-sem"
            let enrollmentCount = 0;

            const processGradeRecord = async (grade: any, isCurrent: boolean, determinedCurrentAcademicYear: number) => {
                const newSemId = seminarianIdMap.get(grade.SemID);
                const newCourseId = courseMap.get(grade.CourseName);

                // Check mappings individually for clearer warnings
                if (!newSemId) {
                     console.warn(`  Skipping grade record for Course '${grade.CourseName}' because Old Seminarian ID ${grade.SemID} could not be mapped to a new Seminarian.`);
                     return;
                }
                if (!newCourseId) {
                    console.warn(`  Skipping grade record for Old Seminarian ID ${grade.SemID} because Course Name '${grade.CourseName}' could not be mapped to a new Course.`);
                    return;
                }

                let academicYear: number;
                if (isCurrent) {
                    // Use the determined current academic year from enrollments for CurrentGrades
                    academicYear = determinedCurrentAcademicYear;
                } else {
                    // Logic for HistoricalGrades (using the Year column)
                    if (grade.Year === null || grade.Year === undefined || typeof grade.Year !== 'string' || grade.Year.trim() === '') {
                        console.warn(`  Skipping historical grade record due to invalid/missing Year: ${grade.Year}, SemID=${grade.SemID}, Course=${grade.CourseName}`);
                        return; // Skip this record
                    }
                    const parsedYear = parseInt(grade.Year);
                    if (isNaN(parsedYear)) {
                        console.warn(`  Skipping historical grade record due to non-numeric Year: ${grade.Year}, SemID=${grade.SemID}, Course=${grade.CourseName}`);
                        return; // Skip this record
                    }
                    // Assume 'Year' column represents the start year of the academic year (e.g., 2023 for 23-24)
                    academicYear = parsedYear;
                }

                // --- Process Semester 1 (Fall) ---
                if (grade.Sem1_Percent !== null || grade.Sem1_Credits !== null) {
                    const offeringKey = `${newCourseId}-${academicYear}-Fall`;
                    let offeringId = courseOfferingCache.get(offeringKey);

                    if (!offeringId) {
                        const [newOffering] = await tx.insert(schema.courseOfferings).values({
                            courseId: newCourseId,
                            academicYear: academicYear,
                            semester: 'Fall',
                        }).returning({ id: schema.courseOfferings.id });
                        offeringId = newOffering.id;
                        courseOfferingCache.set(offeringKey, offeringId);
                    }

                    const enrollmentToInsertSem1: InferInsertModel<typeof schema.courseEnrollments> = {
                        seminarianId: newSemId,
                        courseOfferingId: offeringId,
                        creditsEarned: (grade.Sem1_Credits === null || grade.Sem1_Credits === undefined) ? undefined : grade.Sem1_Credits,
                        percentGrade: (grade.Sem1_Percent === null || grade.Sem1_Percent === undefined) ? undefined : grade.Sem1_Percent,
                    };
                    await tx.insert(schema.courseEnrollments).values(enrollmentToInsertSem1);
                    enrollmentCount++;
                }

                // --- Process Semester 2 (Spring) ---
                if (grade.Sem2_Percent !== null || grade.Sem2_Credits !== null) {
                    // Need to associate with the *correct* academic year for spring
                    // Spring semester belongs to the academic year that *started* the previous fall
                    const springAcademicYear = academicYear; // Assuming 'Year' is the start year

                    const offeringKey = `${newCourseId}-${springAcademicYear}-Spring`;
                    let offeringId = courseOfferingCache.get(offeringKey);

                    if (!offeringId) {
                        const [newOffering] = await tx.insert(schema.courseOfferings).values({
                            courseId: newCourseId,
                            academicYear: springAcademicYear,
                            semester: 'Spring',
                        }).returning({ id: schema.courseOfferings.id });
                        offeringId = newOffering.id;
                        courseOfferingCache.set(offeringKey, offeringId);
                    }

                    const enrollmentToInsertSem2: InferInsertModel<typeof schema.courseEnrollments> = {
                        seminarianId: newSemId,
                        courseOfferingId: offeringId,
                        creditsEarned: (grade.Sem2_Credits === null || grade.Sem2_Credits === undefined) ? undefined : grade.Sem2_Credits,
                        percentGrade: (grade.Sem2_Percent === null || grade.Sem2_Percent === undefined) ? undefined : grade.Sem2_Percent,
                    };
                    await tx.insert(schema.courseEnrollments).values(enrollmentToInsertSem2);
                    enrollmentCount++;
                }
            };

            const oldCurrentGrades = dbSource.prepare('SELECT * FROM CurrentGrades').all() as any[];
            // Pass the determined currentAcademicYear to the processor
            for (const grade of oldCurrentGrades) await processGradeRecord(grade, true, currentAcademicYear);

            const oldHistoricalGrades = dbSource.prepare('SELECT * FROM HistoricalGrades').all() as any[];
            // Pass currentAcademicYear (it won't be used when isCurrent is false, but keeps signature consistent)
            for (const grade of oldHistoricalGrades) await processGradeRecord(grade, false, currentAcademicYear);

            console.log(`  Created ${courseOfferingCache.size} course offerings.`);
            console.log(`  Migrated ${enrollmentCount} course enrollment entries.`);

            // --- 11. Migrate Ordinations ---
            console.log('Migrating Ordinations...');
            const oldOrdinations = dbSource.prepare('SELECT * FROM Ordinations').all() as any[];
            const ordinationIdMap = new Map<number, number>(); // Map<oldId, newId>
            for(const oldO of oldOrdinations) {
                const [newO] = await tx.insert(schema.ordinations).values({
                    date: dateStringToDate(oldO.Date),
                    bishop: oldO.Bishop,
                    placeName: oldO.PlaceName,
                    place: oldO.Place,
                    notes: oldO.Notes,
                }).returning({ id: schema.ordinations.id });
                ordinationIdMap.set(oldO.ID, newO.id);
            }
            console.log(`  Migrated ${ordinationIdMap.size} ordination events.`);

            // --- 12. Migrate Orders ---
            console.log('Migrating Orders...');
            const oldOrders = dbSource.prepare('SELECT * FROM Orders').all() as any[];
            let orderCount = 0;
            for(const oldOr of oldOrders) {
                const newSemId = seminarianIdMap.get(oldOr.SemID);
                const newOrdinationId = ordinationIdMap.get(oldOr.OrdinationsID);

                if (!newSemId || !newOrdinationId) {
                    console.warn(`  Skipping order due to missing seminarian or ordination mapping: SemID=${oldOr.SemID}, OrdID=${oldOr.OrdinationsID}`);
                    continue;
                }
                const orderNameDesc = oldOr.OrderID !== null ? orderNameMap.get(oldOr.OrderID) : undefined;
                const mappedOrderName = schema.ordinationOrderEnumValues.find(e => e === orderNameDesc);
                if (!mappedOrderName) {
                    // Clarified warning message
                    console.warn(`  Skipping order for Old Seminarian ID ${oldOr.SemID}: Could not map Old OrderID ${oldOr.OrderID}. Looked up name '${orderNameDesc || 'Not Found'}', which is not a valid value in the new schema's ordinationOrderEnumValues.`);
                    continue;
                }
                await tx.insert(schema.orders).values({
                    ordinationId: newOrdinationId,
                    seminarianId: newSemId,
                    orderName: mappedOrderName,
                    titulum: oldOr.Titulum,
                });
                orderCount++;
            }
            console.log(`  Migrated ${orderCount} order records.`);

            console.log('Migration transaction completed successfully.');
        }); // End Drizzle transaction

        console.log('Migration data insertion complete.');

    } catch (error) {
        // Drizzle handles rollback automatically on error within the transaction
        console.error("Error during migration transaction:", error);
        // Removed manual rollback logic
        throw error; // Re-throw the error to be caught by the final handler
    } finally {
        // Ensure databases are closed regardless of success or failure
        console.log('Closing database connections...');
        if (dbSource && dbSource.open) {
            dbSource.close();
            console.log('Old database connection closed.');
        }
        if (dbTargetSqlite && dbTargetSqlite.open) {
            dbTargetSqlite.close();
            console.log('New database connection closed.');
        }
    }
}

runMigration()
    .then(() => console.log('Migration script executed successfully.'))
    .catch((error) => {
        console.error("Migration script failed:", error); // More specific error message
        process.exit(1);
    });

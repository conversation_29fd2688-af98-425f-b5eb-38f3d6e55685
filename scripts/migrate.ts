// scripts/migrate.ts
import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { InferInsertModel, InferSelectModel, eq } from 'drizzle-orm';
import path from 'path';

// Assuming your schema is in ../database/schema relative to this script
import * as schema from '../database/schema';
import { hashPassword } from '../app/lib/utils';

// --- Configuration ---
const OLD_DB_PATH = process.env.OLD_DB_PATH || './old_registry.db'; // Path to your original SQLite DB
const NEW_DB_PATH = process.env.NEW_DB_PATH || './new_registry.db'; // Path for the new SQLite DB
const ADMIN_USERNAME = 'admin';
const ADMIN_PASSWORD_HASH = await hashPassword('password');

// --- Types ---
type OldSeminarian = InferSelectModel<typeof schema.seminarians> & { OldID: number }; // Add OldID if needed elsewhere
type OldEnrollment = {
    ID: number;
    SemID: number;
    YearDateID: number | null; // academic year start? e.g. 23
    YearDateName: string | null;
    PreviousStatusCode: number | null;
    NewStatusCode: number | null;
    PreviousYear: number | null; // YearNames ID
    NewYear: number | null;     // YearNames ID
    Notes: string | null;
    EntryDate: string; // Date string
};

// --- Helper Functions ---
function dateStringToDate(dateStr: string | null | undefined): Date | null {
    if (!dateStr) return null;
    let date = new Date(dateStr);
    if (!isNaN(date.getTime())) return date;
    date = new Date(dateStr.replace(' ', 'T'));
    if (!isNaN(date.getTime())) return date;
    console.warn(`Could not parse date: ${dateStr}`);
    return null;
}

function valueToBoolean(boolVal: any): boolean | null {
    if (boolVal === null || boolVal === undefined) return null;
    return Boolean(boolVal);
}

function inferSemester(date: Date | null): (typeof schema.semesterEnumValues)[number] | null {
    if (!date) return null;
    const month = date.getMonth(); // 0 = January, 11 = December
    // Simple logic: Aug-Dec = Fall (months 7-11), Jan-May = Spring (months 0-4)
    // Adjust Jun/Jul if needed (maybe based on academic year?)
    if (month >= 7) return 'Fall';
    if (month <= 4) return 'Spring';
    return null; // Or default to Fall?
}

// --- Main Migration Logic ---
async function runMigration() {
    console.log(`Connecting to old database: ${path.resolve(OLD_DB_PATH)}`);
    const dbSource = new Database(OLD_DB_PATH); // Removed verbose logging

    console.log(`Connecting to new database: ${path.resolve(NEW_DB_PATH)}`);
    const dbTargetSqlite = new Database(NEW_DB_PATH); // Removed verbose logging
    const dbTarget = drizzle(dbTargetSqlite, { schema });

    console.log('Starting data migration...');

    try {
        // Use Drizzle's transaction function
        await dbTarget.transaction(async (tx) => {
            console.log('Beginning migration transaction...');

            // --- 1. Load Mappings ---
            console.log('Loading ID mappings...');
            const statusCodes = dbSource.prepare('SELECT ID, Description FROM StatusCodes').all() as { ID: number, Description: string }[];
            const statusCodeMap = new Map(statusCodes.map(s => [s.ID, s.Description]));

            const yearNames = dbSource.prepare('SELECT ID, Description FROM YearNames').all() as { ID: number, Description: string }[];
            const yearNameMap = new Map(yearNames.map(y => [y.ID, y.Description]));

            const orderNames = dbSource.prepare('SELECT ID, Name, LatinName FROM OrderNames').all() as { ID: number, Name: string, LatinName: string }[];
            const orderNameMap = new Map(orderNames.map(o => [o.ID, o.Name]));

            // --- 2. Migrate Seminarians ---
            console.log('Migrating Seminarians...');
            const oldSeminariansRaw = dbSource.prepare('SELECT * FROM Seminarians').all() as any[];
            const seminarianIdMap = new Map<number, number>(); // Map<oldId, newId>
            const userSeminarianLinkMap = new Map<number, number>(); // Map<newUserId, newSeminarianId> - For linking prefects later
            const newSemIdToOldSemDataMap = new Map<number, any>(); // Map<newSemId, oldSemData> - To update year/sem later

            for (const oldSem of oldSeminariansRaw) {
                const statusDesc = statusCodeMap.get(oldSem.StatusCode);
                const mappedStatus = schema.statusEnumValues.find(e => e === statusDesc) || 'Applicant';

                const isValidEmail = oldSem.Email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(oldSem.Email);

                // Placeholder for year/semester - will be updated after enrollments
                const semToInsert: InferInsertModel<typeof schema.seminarians> = {
                    lastName: oldSem.LastName,
                    firstName: oldSem.FirstName,
                    middleName: oldSem.MiddleName,
                    street1: oldSem.Street1,
                    street2: oldSem.Street2,
                    city: oldSem.City,
                    state: oldSem.State,
                    zip: oldSem.ZIP,
                    country: oldSem.Country,
                    mailingStreet: oldSem.MailingStreet,
                    phone: oldSem.Phone,
                    email: isValidEmail ? oldSem.Email : null,
                    citizenship: oldSem.Citizenship,
                    diocese: oldSem.Diocese,
                    dob: dateStringToDate(oldSem.DOB),
                    birthCity: oldSem.BirthCity,
                    birthState: oldSem.BirthState,
                    birthCountry: oldSem.BirthCountry,
                    status: mappedStatus,
                    roomNumber: oldSem.RoomNum, // Renamed column
                    notes: oldSem.Notes,
                    // academicYear, seminaryYear, semester added to schema but populated later
                };

                const [newSem] = await tx.insert(schema.seminarians).values(semToInsert).returning({ id: schema.seminarians.id });
                seminarianIdMap.set(oldSem.ID, newSem.id);
                newSemIdToOldSemDataMap.set(newSem.id, oldSem); // Store for later update
            }
            console.log(`  Migrated ${seminarianIdMap.size} seminarians.`);

            // --- 3. Migrate Personnel/Users & Roles ---
            console.log('Migrating Users and Roles...');
            const professorMap = new Map<string, number>(); // Map<professorName, newUserId>

            // Get unique professors from old grades tables
            const professors = dbSource.prepare(`
                SELECT DISTINCT Professor FROM CurrentGrades WHERE Professor IS NOT NULL AND Professor != ''
                UNION
                SELECT DISTINCT Professor FROM HistoricalGrades WHERE Professor IS NOT NULL AND Professor != ''
            `).all() as { Professor: string }[];

            for (const prof of professors) {
                console.log(`  Processing Professor: ${prof.Professor}`);
                const username = prof.Professor.toLowerCase().replace(/\s+/g, '');

                // Attempt to find associated seminarian (this logic is weak, improve if possible)
                let associatedSemId: number | undefined = undefined;
                // Try matching name? This is unreliable. A better link is needed ideally.
                // for (const [oldId, newId] of seminarianIdMap.entries()) {
                //    const oldSem = oldSeminariansRaw.find(s => s.ID === oldId);
                //    if (oldSem && `${oldSem.FirstName} ${oldSem.LastName}` === prof.Professor) {
                //        associatedSemId = newId;
                //        break;
                //    }
                // }

                const userToInsert: InferInsertModel<typeof schema.users> = {
                    firstName: prof.Professor, // Assuming name is just stored like this
                    lastName: '',
                    username: username,
                    passwordHash: ADMIN_PASSWORD_HASH,
                    isActive: true,
                    associatedSeminarianId: associatedSemId, // Link if found
                };

                const [newUser] = await tx.insert(schema.users).values(userToInsert).returning({ id: schema.users.id });
                professorMap.set(prof.Professor, newUser.id);
                if (associatedSemId) {
                    userSeminarianLinkMap.set(newUser.id, associatedSemId);
                }

                // Assign 'Professor' role
                await tx.insert(schema.userRoles).values({
                    userId: newUser.id,
                    role: 'Professor',
                });
                console.log(`    -> Created user ID: ${newUser.id} with Professor role`);
            }
            console.log(` Migrated ${professorMap.size} professor users.`);

            // Create default admin user
            console.log('Creating default admin user...');
            const [adminUser] = await tx.insert(schema.users).values({
                username: ADMIN_USERNAME,
                passwordHash: ADMIN_PASSWORD_HASH,
                firstName: 'Admin',
                lastName: 'User',
                isActive: true,
            }).returning({ id: schema.users.id });

            // Assign 'Administrator' role to admin
            await tx.insert(schema.userRoles).values({
                userId: adminUser.id,
                role: 'Administrator',
            });
            console.log(`  -> Created admin user ID: ${adminUser.id} with Administrator role`);

            // --- 4. Migrate Departments ---
            console.log('Migrating Departments...');
            const oldDepartments = dbSource.prepare('SELECT * FROM Departments').all() as any[];
            const departmentMap = new Map<number, number>(); // Map<oldDeptId, newDeptId>
            for (const oldDept of oldDepartments) {
                const category = schema.departmentCategoryEnumValues.find(c => c === oldDept.Category) || 'General'; // Default category

                // Find the new user ID for the prefect (using old SemID referenced by Prefect column)
                let newPrefectUserId: number | null = null;
                if (oldDept.Prefect) {
                    const prefectSemId = seminarianIdMap.get(oldDept.Prefect);
                    if (prefectSemId) {
                        // Find user associated with this seminarian ID
                        for (const [userId, semId] of userSeminarianLinkMap.entries()) {
                            if (semId === prefectSemId) {
                                newPrefectUserId = userId;
                                break;
                            }
                        }
                        if (!newPrefectUserId) {
                            console.warn(`  Could not find associated user for Prefect Seminarian ID ${prefectSemId} (Old Prefect ID: ${oldDept.Prefect}) for Dept "${oldDept.DeptName}"`);
                        }
                    } else {
                        console.warn(`  Could not map old Prefect ID ${oldDept.Prefect} to new Seminarian ID for Dept "${oldDept.DeptName}"`);
                    }
                }

                const [newDept] = await tx.insert(schema.departments).values({
                    name: oldDept.DeptName,
                    category: category,
                    prefectId: newPrefectUserId,
                }).returning({ id: schema.departments.id });
                departmentMap.set(oldDept.ID, newDept.id);
            }
            console.log(`  Migrated ${departmentMap.size} departments.`);

            // --- 5. Migrate Department Assignments ---
            console.log('Migrating Department Assignments...');
            const oldAssignments = dbSource.prepare('SELECT * FROM DepartmentAssignments').all() as any[];
            let assignmentCount = 0;
            for (const oldAssign of oldAssignments) {
                const newSemId = seminarianIdMap.get(oldAssign.SemID);
                const newDeptId = departmentMap.get(oldAssign.DeptID);

                if (!newSemId) {
                    console.warn(`  Skipping assignment for unknown old SemID: ${oldAssign.SemID}, DeptID: ${oldAssign.DeptID}`);
                    continue;
                }
                if (!newDeptId) {
                    console.warn(`  Skipping assignment for unknown old DeptID: ${oldAssign.DeptID}, SemID: ${oldAssign.SemID}`);
                    continue;
                }

                // Insert ignore duplicate PK errors if any (though shouldn't happen with clean target)
                try {
                    await tx.insert(schema.departmentAssignments).values({
                        seminarianId: newSemId,
                        departmentId: newDeptId,
                    });
                    assignmentCount++;
                } catch (e: any) {
                    if (e.code === 'SQLITE_CONSTRAINT_PRIMARYKEY') {
                        console.warn(`  Duplicate assignment skipped: SemID=${newSemId}, DeptID=${newDeptId}`);
                    } else {
                        throw e; // Re-throw other errors
                    }
                }
            }
            console.log(`  Migrated ${assignmentCount} department assignments.`);

            // --- 6. Migrate Applications (formerly Document Files) ---
            console.log('Migrating Applications...');
            const oldDocs = dbSource.prepare('SELECT * FROM DocumentFile').all() as any[];
            let appCount = 0;
            for (const oldDoc of oldDocs) {
                const newSemId = seminarianIdMap.get(oldDoc.SemID);
                if (!newSemId) {
                    console.warn(`  Skipping application for unknown old SemID: ${oldDoc.SemID}`);
                    continue;
                }
                const appToInsert: InferInsertModel<typeof schema.applications> = {
                    seminarianId: newSemId,
                    applicationEntered: dateStringToDate(oldDoc.ApplicationEntered),
                    acceptanceSent: dateStringToDate(oldDoc.AcceptanceSent),
                    baptismalCertificate: valueToBoolean(oldDoc.BaptismalCertificate),
                    confirmationCertificate: valueToBoolean(oldDoc.ConfirmationCertificate),
                    catholicMarriage: valueToBoolean(oldDoc.CatholicMarriage),
                    marriageCertificate: valueToBoolean(oldDoc.MarriageCertificate),
                    academicTranscripts: valueToBoolean(oldDoc.AcademicTranscripts),
                    referenceLetter: valueToBoolean(oldDoc.ReferenceLetter),
                    referenceLetterFrom: oldDoc.ReferenceLetterFrom,
                    applicationNotes: oldDoc.ApplicationNotes,
                    sspxSchool: oldDoc.SSPXSchool,
                    perpetualEngagement: dateStringToDate(oldDoc.PerpetualEngagement),
                    skills: oldDoc.Skills,
                    musicalAbility: oldDoc.MusicalAbility,
                    notes: oldDoc.OtherNotes, // Renamed column
                };
                await tx.insert(schema.applications).values(appToInsert);
                appCount++;
            }
            console.log(`  Migrated ${appCount} application records.`);

            // --- 7. Migrate Enrollments & Status Updates ---
            console.log('Migrating Enrollments and Status Updates...');
            const oldEnrolls = dbSource.prepare('SELECT * FROM Enrollments ORDER BY SemID, EntryDate ASC').all() as OldEnrollment[];
            let enrollCount = 0;
            let statusUpdateCount = 0;
            const latestSemInfo = new Map<number, { year: any; sem: any; acadYear: number | null }>(); // Map<newSemId, {year, sem, acadYear}>

            for(const oldEn of oldEnrolls) {
                const newSemId = seminarianIdMap.get(oldEn.SemID);
                if (!newSemId) {
                    console.warn(`  Skipping enrollment/status update for unknown old SemID: ${oldEn.SemID}`);
                    continue;
                }

                const entryDateObj = dateStringToDate(oldEn.EntryDate);
                if (!entryDateObj) {
                    console.warn(`  Skipping enrollment/status update due to invalid EntryDate for old SemID: ${oldEn.SemID}, old Enrollment ID: ${oldEn.ID}`);
                    continue; // Need date for status updates and enrollment
                }

                const newStatusDesc = oldEn.NewStatusCode !== null ? statusCodeMap.get(oldEn.NewStatusCode) : undefined;
                const mappedNewStatus = schema.statusEnumValues.find(e => e === newStatusDesc);

                const newYearDesc = oldEn.NewYear !== null ? yearNameMap.get(oldEn.NewYear) : undefined;
                const mappedNewYear = schema.seminaryYearEnumValues.find(e => e === newYearDesc);
                const inferredSemester = inferSemester(entryDateObj);
                const academicYear = oldEn.YearDateID; // Use this as the academic year identifier

                // Insert into new enrollments table (state at the time of entry)
                if (mappedNewYear) { // Only create enrollment if year changed/set
                    await tx.insert(schema.enrollments).values({
                        seminarianId: newSemId,
                        academicYear: academicYear,
                        seminaryYear: mappedNewYear,
                        semester: inferredSemester,
                        entryDate: entryDateObj,
                        notes: oldEn.Notes,
                    });
                    enrollCount++;
                    // Track latest info for updating seminarian record
                    latestSemInfo.set(newSemId, { year: mappedNewYear, sem: inferredSemester, acadYear: academicYear });
                } else {
                    console.warn(`  Could not map NewYear ID ${oldEn.NewYear} for enrollment record, SemID=${oldEn.SemID}`);
                }

                // Insert into status updates if status changed
                const prevStatusDesc = oldEn.PreviousStatusCode !== null ? statusCodeMap.get(oldEn.PreviousStatusCode) : undefined;
                const mappedPrevStatus = schema.statusEnumValues.find(e => e === prevStatusDesc);
                if (mappedNewStatus && mappedNewStatus !== mappedPrevStatus) {
                    await tx.insert(schema.statusUpdates).values({
                        seminarianId: newSemId,
                        status: mappedNewStatus,
                        updateDate: entryDateObj,
                        notes: oldEn.Notes, // Maybe prefix notes? e.g. "Enrollment Note: ..."
                    });
                    statusUpdateCount++;
                    // Update latest info map with status if relevant to seminarian record
                } else if (mappedNewStatus && !mappedPrevStatus && mappedNewStatus !== 'Applicant'){
                    // Handle initial status set from Applicant/Null
                    await tx.insert(schema.statusUpdates).values({
                        seminarianId: newSemId,
                        status: mappedNewStatus,
                        updateDate: entryDateObj,
                        notes: oldEn.Notes ?? 'Initial status update',
                    });
                    statusUpdateCount++;
                }
            }
            console.log(`  Migrated ${enrollCount} enrollment records.`);
            console.log(`  Created ${statusUpdateCount} status update records.`);

            // --- 7b. Update Seminarians with latest Year/Semester ---
            console.log('Updating Seminarian records with latest year/semester...');
            let semUpdateCount = 0;
            for (const [newSemId, info] of latestSemInfo.entries()) {
                await tx.update(schema.seminarians)
                    .set({
                        seminaryYear: info.year,
                        semester: info.sem,
                    })
                    .where(eq(schema.seminarians.id, newSemId));
                semUpdateCount++;
            }
            console.log(`  Updated ${semUpdateCount} seminarian records.`);

            // --- 8. Migrate Room Assignments ---
            console.log('Migrating Room Assignments...');
            let roomAssignCount = 0;
            const migrationDate = new Date(); // Use a consistent date for initial assignments
            for (const oldSem of oldSeminariansRaw) {
                if (oldSem.RoomNum !== null && oldSem.RoomNum !== undefined) {
                    const newSemId = seminarianIdMap.get(oldSem.ID);
                    if (newSemId) {
                        await tx.insert(schema.roomAssignments).values({
                            seminarianId: newSemId,
                            roomNumber: oldSem.RoomNum,
                            assignedDate: migrationDate, // Set initial assign date
                            vacatedDate: null,
                            notes: 'Migrated from old system',
                        });
                        roomAssignCount++;
                    }
                }
            }
            console.log(`  Created ${roomAssignCount} room assignments.`);

            // --- 9. Migrate Courses ---
            console.log('Migrating Courses...');
            const oldCourses = dbSource.prepare(`
                SELECT DISTINCT CourseName, MAX(credits) as Credits
                FROM (
                    SELECT CourseName, Sem1_PotentialCredits as credits FROM CurrentGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                    UNION ALL
                    SELECT CourseName, Sem2_PotentialCredits as credits FROM CurrentGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                    UNION ALL
                    SELECT CourseName, Sem1_PotentialCredits as credits FROM HistoricalGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                    UNION ALL
                    SELECT CourseName, Sem2_PotentialCredits as credits FROM HistoricalGrades WHERE CourseName IS NOT NULL AND CourseName != ''
                )
                WHERE credits IS NOT NULL
                GROUP BY CourseName
            `).all() as { CourseName: string, Credits: number }[];
            const courseMap = new Map<string, number>(); // Map<courseName, newCourseId>
            for(const oldC of oldCourses) {
                const [newCourse] = await tx.insert(schema.courses).values({
                    courseName: oldC.CourseName,
                    credits: oldC.Credits || 0, // Default credits to 0 if null
                }).returning({ id: schema.courses.id });
                courseMap.set(oldC.CourseName, newCourse.id);
            }
            console.log(`  Migrated ${courseMap.size} unique courses.`);

            // --- Determine Current Academic Year ---
            console.log('Determining current academic year from old enrollments...');
            let currentAcademicYear: number;
            try {
                const maxYearResult = dbSource.prepare('SELECT MAX(YearDateID) as MaxYear FROM Enrollments WHERE YearDateID IS NOT NULL').get() as { MaxYear: number | null };
                if (maxYearResult && maxYearResult.MaxYear !== null) {
                    // Assuming YearDateID is like 23, convert to 2023. Handles years YY >= 70 as 19YY, otherwise 20YY.
                    currentAcademicYear = maxYearResult.MaxYear >= 70 ? 1900 + maxYearResult.MaxYear : 2000 + maxYearResult.MaxYear;
                    console.log(`  Determined current academic year as: ${currentAcademicYear}`);
                } else {
                    console.warn('Could not determine current academic year from enrollments, falling back to current calendar year.');
                    currentAcademicYear = new Date().getFullYear();
                }
            } catch (err) {
                 console.error('Error querying max YearDateID from old Enrollments:', err);
                 console.warn('Falling back to current calendar year.');
                 currentAcademicYear = new Date().getFullYear();
            }

            // --- 10. Migrate Grades & Course Offerings (Combined Logic) ---
            console.log('Migrating Grades and creating Course Offerings...');
            const courseOfferingCache = new Map<string, number>(); // Key: "courseId-userId-acadYear-sem"
            let gradeCount = 0;

            const processGradeRecord = async (grade: any, isCurrent: boolean, determinedCurrentAcademicYear: number) => {
                const newSemId = seminarianIdMap.get(grade.SemID);
                const newCourseId = courseMap.get(grade.CourseName);
                const newUserId = professorMap.get(grade.Professor); // Professor mapped to user ID

                // Check mappings individually for clearer warnings
                if (!newSemId) {
                     console.warn(`  Skipping grade record for Course '${grade.CourseName}' because Old Seminarian ID ${grade.SemID} could not be mapped to a new Seminarian.`);
                     return;
                }
                if (!newCourseId) {
                    console.warn(`  Skipping grade record for Old Seminarian ID ${grade.SemID} because Course Name '${grade.CourseName}' could not be mapped to a new Course.`);
                    return;
                }
                if (!newUserId) {
                    console.warn(`  Skipping grade record for Old Seminarian ID ${grade.SemID}, Course '${grade.CourseName}' because Professor '${grade.Professor}' could not be mapped to a new User.`);
                    return;
                }

                let academicYear: number;
                if (isCurrent) {
                    // Use the determined current academic year from enrollments for CurrentGrades
                    academicYear = determinedCurrentAcademicYear;
                } else {
                    // Logic for HistoricalGrades (using the Year column)
                    if (grade.Year === null || grade.Year === undefined || typeof grade.Year !== 'string' || grade.Year.trim() === '') {
                        console.warn(`  Skipping historical grade record due to invalid/missing Year: ${grade.Year}, SemID=${grade.SemID}, Course=${grade.CourseName}`);
                        return; // Skip this record
                    }
                    const parsedYear = parseInt(grade.Year);
                    if (isNaN(parsedYear)) {
                        console.warn(`  Skipping historical grade record due to non-numeric Year: ${grade.Year}, SemID=${grade.SemID}, Course=${grade.CourseName}`);
                        return; // Skip this record
                    }
                    // Assume 'Year' column represents the start year of the academic year (e.g., 2023 for 23-24)
                    academicYear = parsedYear;
                }

                // --- Process Semester 1 (Fall) ---
                if (grade.Sem1_Percent !== null || grade.Sem1_Credits !== null) {
                    const offeringKey = `${newCourseId}-${newUserId}-${academicYear}-Fall`;
                    let offeringId = courseOfferingCache.get(offeringKey);

                    if (!offeringId) {
                        const [newOffering] = await tx.insert(schema.courseOfferings).values({
                            courseId: newCourseId,
                            userId: newUserId,
                            academicYear: academicYear,
                            semester: 'Fall',
                            // seminaryYear: null, // This field was removed from courseOfferings
                        }).returning({ id: schema.courseOfferings.id });
                        offeringId = newOffering.id;
                        courseOfferingCache.set(offeringKey, offeringId);
                    }

                    const gradeToInsertSem1: InferInsertModel<typeof schema.grades> = {
                        seminarianId: newSemId,
                        courseOfferingId: offeringId,
                        creditsEarned: (grade.Sem1_Credits === null || grade.Sem1_Credits === undefined) ? undefined : grade.Sem1_Credits,
                        percentGrade: (grade.Sem1_Percent === null || grade.Sem1_Percent === undefined) ? undefined : grade.Sem1_Percent,
                        // letterGrade was removed from schema
                    };
                    await tx.insert(schema.grades).values(gradeToInsertSem1);
                    gradeCount++;
                }

                // --- Process Semester 2 (Spring) ---
                if (grade.Sem2_Percent !== null || grade.Sem2_Credits !== null) {
                    // Need to associate with the *correct* academic year for spring
                    // Spring semester belongs to the academic year that *started* the previous fall
                    const springAcademicYear = academicYear; // Assuming 'Year' is the start year

                    const offeringKey = `${newCourseId}-${newUserId}-${springAcademicYear}-Spring`;
                    let offeringId = courseOfferingCache.get(offeringKey);

                    if (!offeringId) {
                        const [newOffering] = await tx.insert(schema.courseOfferings).values({
                            courseId: newCourseId,
                            userId: newUserId,
                            academicYear: springAcademicYear,
                            semester: 'Spring',
                            // seminaryYear: null, // This field was removed from courseOfferings
                        }).returning({ id: schema.courseOfferings.id });
                        offeringId = newOffering.id;
                        courseOfferingCache.set(offeringKey, offeringId);
                    }

                    const gradeToInsertSem2: InferInsertModel<typeof schema.grades> = {
                        seminarianId: newSemId,
                        courseOfferingId: offeringId,
                        creditsEarned: (grade.Sem2_Credits === null || grade.Sem2_Credits === undefined) ? undefined : grade.Sem2_Credits,
                        percentGrade: (grade.Sem2_Percent === null || grade.Sem2_Percent === undefined) ? undefined : grade.Sem2_Percent,
                        // letterGrade was removed from schema
                    };
                    await tx.insert(schema.grades).values(gradeToInsertSem2);
                    gradeCount++;
                }
            };

            const oldCurrentGrades = dbSource.prepare('SELECT * FROM CurrentGrades').all() as any[];
            // Pass the determined currentAcademicYear to the processor
            for (const grade of oldCurrentGrades) await processGradeRecord(grade, true, currentAcademicYear);

            const oldHistoricalGrades = dbSource.prepare('SELECT * FROM HistoricalGrades').all() as any[];
            // Pass currentAcademicYear (it won't be used when isCurrent is false, but keeps signature consistent)
            for (const grade of oldHistoricalGrades) await processGradeRecord(grade, false, currentAcademicYear);

            console.log(`  Created ${courseOfferingCache.size} course offerings.`);
            console.log(`  Migrated ${gradeCount} grade entries.`);

            // --- 11. Migrate Ordinations ---
            console.log('Migrating Ordinations...');
            const oldOrdinations = dbSource.prepare('SELECT * FROM Ordinations').all() as any[];
            const ordinationIdMap = new Map<number, number>(); // Map<oldId, newId>
            for(const oldO of oldOrdinations) {
                const [newO] = await tx.insert(schema.ordinations).values({
                    date: dateStringToDate(oldO.Date),
                    bishop: oldO.Bishop,
                    placeName: oldO.PlaceName,
                    place: oldO.Place,
                    notes: oldO.Notes,
                }).returning({ id: schema.ordinations.id });
                ordinationIdMap.set(oldO.ID, newO.id);
            }
            console.log(`  Migrated ${ordinationIdMap.size} ordination events.`);

            // --- 12. Migrate Orders ---
            console.log('Migrating Orders...');
            const oldOrders = dbSource.prepare('SELECT * FROM Orders').all() as any[];
            let orderCount = 0;
            for(const oldOr of oldOrders) {
                const newSemId = seminarianIdMap.get(oldOr.SemID);
                const newOrdinationId = ordinationIdMap.get(oldOr.OrdinationsID);

                if (!newSemId || !newOrdinationId) {
                    console.warn(`  Skipping order due to missing seminarian or ordination mapping: SemID=${oldOr.SemID}, OrdID=${oldOr.OrdinationsID}`);
                    continue;
                }
                const orderNameDesc = oldOr.OrderID !== null ? orderNameMap.get(oldOr.OrderID) : undefined;
                const mappedOrderName = schema.ordinationOrderEnumValues.find(e => e === orderNameDesc);
                if (!mappedOrderName) {
                    // Clarified warning message
                    console.warn(`  Skipping order for Old Seminarian ID ${oldOr.SemID}: Could not map Old OrderID ${oldOr.OrderID}. Looked up name '${orderNameDesc || 'Not Found'}', which is not a valid value in the new schema's ordinationOrderEnumValues.`);
                    continue;
                }
                await tx.insert(schema.orders).values({
                    ordinationId: newOrdinationId,
                    seminarianId: newSemId,
                    orderName: mappedOrderName,
                    titulum: oldOr.Titulum,
                });
                orderCount++;
            }
            console.log(`  Migrated ${orderCount} order records.`);

            console.log('Migration transaction completed successfully.');
        }); // End Drizzle transaction

        console.log('Migration data insertion complete.');

    } catch (error) {
        // Drizzle handles rollback automatically on error within the transaction
        console.error("Error during migration transaction:", error);
        // Removed manual rollback logic
        throw error; // Re-throw the error to be caught by the final handler
    } finally {
        // Ensure databases are closed regardless of success or failure
        console.log('Closing database connections...');
        if (dbSource && dbSource.open) {
            dbSource.close();
            console.log('Old database connection closed.');
        }
        if (dbTargetSqlite && dbTargetSqlite.open) {
            dbTargetSqlite.close();
            console.log('New database connection closed.');
        }
    }
}

runMigration()
    .then(() => console.log('Migration script executed successfully.'))
    .catch((error) => {
        console.error("Migration script failed:", error); // More specific error message
        process.exit(1);
    });

// import { eq } from "drizzle-orm";
// import { database } from "~/database/context";
// import { users, type ResourceType } from "~/database/schema";

// interface UserRolePermissionAttribute {
//   name: string;
//   query: string;
// }

// interface UserRolePermission {
//   resourceType: ResourceType;
//   resourceAction: string;
//   attributes: UserRolePermissionAttribute[];
// }

// interface UserRole {
//   name: string;
//   permissions: UserRolePermission[];
// }

// export default class UsersRepository {
//   constructor() {}

//   async getUserRoles(userId: number) {
//     const db = database();

//     const user = await db.query.users.findFirst({
//       where: eq(users.id, userId),
//       columns: {},
//       with: {
//         userRoles: {
//           columns: {},
//           with: {
//             role: {
//               columns: {
//                 name: true,
//               },
//               with: {
//                 permissions: {
//                   columns: {
//                     resourceAction: true,
//                     resourceType: true,
//                   },
//                   with: {
//                     attributes: {
//                       with: {
//                         attribute: {
//                           columns: {
//                             name: true,
//                             query: true,
//                             resourceAction: true,
//                             resourceType: true,
//                           },
//                         },
//                       },
//                     },
//                   },
//                 },
//               },
//             },
//           },
//         },
//       },
//     });

//     const roles = user?.userRoles?.map<UserRole>((ur) => ({
//       name: ur.role.name,
//       permissions: ur.role.permissions.map((p) => ({
//         resourceType: p.resourceType,
//         resourceAction: p.resourceAction,
//         attributes: p.attributes
//           .filter(
//             (a) =>
//               a.attribute.resourceType === p.resourceType &&
//               a.attribute.resourceAction === p.resourceAction
//           )
//           .map((a) => ({
//             name: a.attribute.name,
//             query: a.attribute.query,
//           })),
//       })),
//     }));

//     return roles || [];
//   }
// }

import { useCallback } from "react";
import { useSearchParams } from "react-router";
import { generateFormData } from "remix-hook-form";
import type { z, ZodObject } from "zod";
import { objectToURLSearchParams } from "~/lib/utils";

export function useSearchState<T extends z.ZodRawShape>(schema: ZodObject<T>) {
  const [searchParams, setSearchParams] = useSearchParams();

  const { data: searchState } = schema.safeParse(
    generateFormData(searchParams)
  );

  const setSearchState = useCallback(
    (
      nextInit:
        | z.infer<typeof schema>
        | ((prev: z.infer<typeof schema>) => z.infer<typeof schema>)
    ) => {
      if (typeof nextInit == "function") {
        setSearchParams((sp) => {
          const { data } = schema.safeParse(generateFormData(sp));
          return objectToURLSearchParams(nextInit(data || ({} as any)));
        });
      } else {
        setSearchParams(objectToURLSearchParams(nextInit));
      }
    },
    [setSearchParams, schema]
  );

  return [
    (searchState || {}) as z.infer<typeof schema>,
    setSearchState,
  ] as const;
}

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export async function hashPassword(password: string) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password);

  const hashBuffer = await crypto.subtle.digest("SHA-512", data);

  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("");
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  }).format(date);
}

export function sanitizeDefaultValues(obj: any) {
  return Object.fromEntries(Object.entries(obj).filter(([, v]) => v !== null));
}

//Found at: https://www.w3resource.com/javascript-exercises/javascript-date-exercise-18.php#google_vignette
export function calculateAge(dob: Date) { 
  // Calculate the difference in milliseconds between the current date and the provided date of birth
  var diff_ms = Date.now() - dob.getTime();
  // Create a new Date object representing the difference in milliseconds and store it in the variable age_dt (age Date object)
  var age_dt = new Date(diff_ms); 

  // Calculate the absolute value of the difference in years between the age Date object and the year 1970 (UNIX epoch)
  return Math.abs(age_dt.getUTCFullYear() - 1970);
}

export const objectToURLSearchParams = (
  obj: any,
  params: URLSearchParams = new URLSearchParams(),
  prefix: string = ""
): URLSearchParams => {
  if (obj === null || obj === undefined) {
    return params;
  }

  if (obj instanceof File || obj instanceof Blob) {
    if (prefix) {
      console.warn(
        `Skipping File/Blob at key "${prefix}" as URLSearchParams only supports strings.`
      );
    } else {
      console.warn("Skipping File/Blob without a key name.");
    }
    return params;
  }

  if (obj instanceof Date) {
    if (!prefix) {
      console.warn("Cannot append Date without a key name.");
      return params;
    }
    params.append(prefix, obj.toISOString());
    return params;
  }

  if (Array.isArray(obj)) {
    obj.forEach((item, index) => {
      const arrayKey = `${prefix}[${index}]`;
      objectToURLSearchParams(item, params, arrayKey);
    });
    return params;
  }

  if (typeof obj === "object") {
    Object.keys(obj).forEach((key) => {
      const value = obj[key];
      const newPrefix = prefix ? `${prefix}.${key}` : key;
      objectToURLSearchParams(value, params, newPrefix);
    });
    return params;
  }

  if (
    typeof obj === "string" ||
    typeof obj === "number" ||
    typeof obj === "boolean"
  ) {
    if (!prefix) {
      console.warn("Cannot append primitive value without a key name.");
      return params;
    }
    params.append(prefix, String(obj));
    return params;
  }

  console.warn(
    `Unsupported type encountered at key "${prefix}": ${typeof obj}`
  );
  return params;
};

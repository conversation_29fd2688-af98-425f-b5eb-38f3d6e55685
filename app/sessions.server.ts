import { createCookieSessionStorage } from "react-router";

export type SessionData = {
  userId: number;
};

export type SessionFlashData = {
};

const { getSession, commitSession, destroySession } =
  createCookieSessionStorage<SessionData, SessionFlashData>(
    {
      cookie: {
        name: "__session",
        httpOnly: true,
        maxAge: 24 * 60 * 60, // 1 day
        path: "/",
        sameSite: "lax",
        secrets: [process.env.SESSION_SECRET!],
        secure: true,
      },
    }
  );

export { getSession, commitSession, destroySession };

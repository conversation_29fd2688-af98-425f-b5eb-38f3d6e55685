import { MessageCircleWarning } from "lucide-react";
import { createPortal } from 'react-dom';
import { useRemixFormContext } from "remix-hook-form";
import { ClientOnly } from "remix-utils/client-only";
import { cn } from "~/lib/utils";
import { Button } from "./ui/button";
import { useNavigate, useNavigation } from "react-router";

export default function FormSubmitBar({message, isNew}: { message?: string, isNew?: boolean }) {
  const form = useRemixFormContext();
  const navigate = useNavigate();

  const isDirty = form.formState.isDirty;

  return (
    <ClientOnly>
      {() =>
        createPortal(
          <div
            className={cn(
              "z-100 fixed left-0 top-0 right-0 flex justify-between px-6 py-2 bg-foreground shadow-xl transition-opacity",
              isDirty ? "opacity-100" : "opacity-0 pointer-events-none"
            )}
          >
            <label className="text-muted text-sm flex items-center">
              <MessageCircleWarning className="mr-2" /> {message || (isNew ? 'Unsaved new record' : 'Unsaved changes')}
            </label>
            <div className="flex gap-2">
              <Button variant="default" onClick={() => isNew ? navigate(-1) : form.reset()}>Discard</Button>
              <Button type="submit" variant="secondary" onClick={() => form.handleSubmit()}>
                Save
              </Button>
            </div>
          </div>,
          document.body
        )
      }
    </ClientOnly>
  );
}

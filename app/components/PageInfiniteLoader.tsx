import { useEffect, useRef } from "react";
import { useNavigation } from "react-router";
import { cn } from "~/lib/utils";

export default function PageInfiniteLoader({
  halfLifeMS = 1000,
}: {
  halfLifeMS?: number;
}) {
  const nav = useNavigation();
  const barRef = useRef<HTMLDivElement>(null);
  const isNavigating = nav.state !== "idle";

  useEffect(() => {
    if (!isNavigating) return;

    let play = true;
    const tI = Date.now();
    function animate() {
      if (!play) {
        if (barRef.current) barRef.current.style.width = `0%`;
        return;
      }

      const t = (Date.now() - tI) / halfLifeMS;
      const p = 1 - 1 / (t * t + 1);

      if (barRef.current) barRef.current.style.width = `${p * 100}%`;

      requestAnimationFrame(animate);
    }

    requestAnimationFrame(animate);
    return () => {
      play = false;
    };
  }, [isNavigating, nav.formAction, nav.location]);

  return (
    <div
      className={cn(
        "fixed top-0 left-0 right-0 h-1 bg-muted z-[100] overflow-clip",
        { hidden: !isNavigating }
      )}
    >
      <div
        ref={barRef}
        className="h-full bg-primary rounded-tr-sm rounded-br-sm"
      ></div>
    </div>
  );
}

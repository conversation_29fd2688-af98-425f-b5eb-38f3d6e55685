import {
  type ColumnDef,
  type ColumnSort,
  flexRender,
  getCoreRowModel,
  type RowSelectionState,
  useReactTable,
} from "@tanstack/react-table";

import { zodResolver } from "@hookform/resolvers/zod";
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  EllipsisVertical,
  X,
} from "lucide-react";
import { type ReactNode, useCallback } from "react";
import { z } from "zod";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import { useSearchState } from "~/hooks/use-search-state";
import { Button } from "./ui/button";
import { Checkbox } from "./ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import { Input } from "./ui/input";

export interface DataTableActions { icon?: ReactNode; label: ReactNode; onClick: () => void };

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  idKey: string;
  defaultPageSize?: number;
  actions?: DataTableActions[];
  selection?: RowSelectionState;
  setSelection?: React.Dispatch<React.SetStateAction<RowSelectionState>>;
}

export const tableParamsSchema = z.object({
  search: z.string().optional(),
  limit: z.number().min(1).max(100).optional(),
  offset: z.number().min(0).optional(),
  sort: z.record(z.string(), z.enum(["asc", "desc"])).optional(),
});

export const SELECT_COLUMN: ColumnDef<any, any> = {
  id: "select",
  header: ({ table }) => (
    <Checkbox
      checked={
        table.getIsAllPageRowsSelected() ||
        (table.getIsSomePageRowsSelected() && "indeterminate")
      }
      onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
      aria-label="Select all"
    />
  ),
  cell: ({ row }) => (
    <Checkbox
      checked={row.getIsSelected()}
      onCheckedChange={(value) => row.toggleSelected(!!value)}
      aria-label="Select row"
    />
  ),
  enableSorting: false,
  enableHiding: false,
};

type TableParams = z.infer<typeof tableParamsSchema>;
export const tableParamsResolver = zodResolver(tableParamsSchema);

const convertToColumnSort = (sort: TableParams["sort"]): ColumnSort[] =>
  Object.entries(sort || {}).map(([key, value]) => ({
    id: key,
    desc: value == "desc",
  }));

const convertFromColumnSort = (sort: ColumnSort[]): TableParams["sort"] =>
  sort.reduce((acc, curr) => {
    acc[curr.id] = curr.desc ? "desc" : "asc";
    return acc;
  }, {} as NonNullable<TableParams["sort"]>);

const convertToPagination = (
  { limit, offset }: Pick<TableParams, "limit" | "offset">,
  fallbackLimit = 4
): {
  pageIndex: number;
  pageSize: number;
} => ({
  pageIndex: Math.floor((offset || 0) / (limit || fallbackLimit)),
  pageSize: limit || fallbackLimit,
});

const convertFromPagination = ({
  pageIndex,
  pageSize,
}: {
  pageIndex: number;
  pageSize: number;
}): Pick<TableParams, "limit" | "offset"> => ({
  offset: pageIndex * pageSize,
  limit: pageSize,
});

export function DataTable<TData, TValue>({
  columns,
  data,
  defaultPageSize = 10,
  actions,
  idKey,
  selection,
  setSelection,
}: DataTableProps<TData, TValue>) {
  const [searchState, setSearchState] = useSearchState(tableParamsSchema);
  const offset = searchState.offset || 0;
  const limit = searchState.limit || defaultPageSize;

  const enableRowSelection = Boolean(setSelection && selection && idKey);
  const table = useReactTable({
    data: data?.slice(0, limit),
    columns: enableRowSelection ? [SELECT_COLUMN as any, ...columns] : columns,
    getCoreRowModel: getCoreRowModel(),
    pageCount: Math.floor(offset / limit) + 1 + (data?.length > limit ? 1 : 0),
    manualPagination: true,
    getRowId: idKey ? (row, i) => (row as any)[idKey!] ?? i : undefined,
    enableRowSelection,
    onRowSelectionChange: setSelection,
    onSortingChange: (updaterOrValue) => {
      if (typeof updaterOrValue == "function") {
        setSearchState((prev) => ({
          ...prev,
          sort: convertFromColumnSort(
            updaterOrValue(convertToColumnSort(prev.sort))
          ),
        }));
      } else {
        setSearchState((prev) => ({
          ...prev,
          sort: convertFromColumnSort(updaterOrValue),
        }));
      }
    },
    onPaginationChange: (updaterOrValue) => {
      if (typeof updaterOrValue == "function") {
        setSearchState((prev) => ({
          ...prev,
          ...convertFromPagination(
            updaterOrValue(convertToPagination(prev, defaultPageSize))
          ),
        }));
      } else {
        setSearchState((prev) => ({
          ...prev,
          ...convertFromPagination(updaterOrValue),
        }));
      }
    },
    state: {
      sorting: convertToColumnSort(searchState.sort),
      pagination: convertToPagination(searchState, defaultPageSize),
      rowSelection: selection || undefined,
    },
  });

  const searchChanged = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchState((prev) => ({ ...prev, search: e.target.value }));
    },
    []
  );

  const selectionCount = Object.keys(selection || {}).length;

  return (
    <div className="space-y-2">
      <div className="flex gap-4 justify-between">
        <Input
          type="text"
          value={searchState.search}
          onChange={searchChanged}
          placeholder="Search..."
        />
        {Boolean(actions?.length) && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="secondary">
                More actions <ChevronDown />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              {actions?.map((action, index) => (
                <DropdownMenuItem key={index} onClick={action.onClick}>
                  {action.icon} {action.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between">
        {Boolean(selectionCount) ? (
          <p className="text-sm text-muted-foreground hover:[&>button]:inline">
            {selectionCount} selected{" "}
            <button
              onClick={setSelection ? () => setSelection({}) : undefined}
              className="hidden size-4 cursor-pointer align-text-bottom"
            >
              <X className="size-full" />
            </button>
          </p>
        ) : (
          <p></p>
        )}
        <div className="flex items-center">
          <p className="mr-4 text-sm text-muted-foreground">
            {offset + 1}-{offset + limit}
          </p>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="rounded-r-none"
          >
            <ChevronLeft />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="rounded-l-none"
          >
            <ChevronRight />
          </Button>
        </div>
      </div>
    </div>
  );
}

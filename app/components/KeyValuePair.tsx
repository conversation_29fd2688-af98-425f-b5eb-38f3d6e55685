import type { ReactNode } from "react";


interface KeyValuePairProps {
  label: string;
  value: ReactNode;
}

export function KeyValuePair({ label, value }: KeyValuePairProps) {
  return (
    <div className="grid grid-cols-2">
      <span className="text-muted-foreground">{label}:</span>
      <span>{value}</span>
    </div>
  );
}

interface KeyValueGroupProps {
  title: string;
  children: ReactNode;
}

export function KeyValueGroup({ title, children }: KeyValueGroupProps) {
  return (
    <div>
      <h3 className="text-lg font-medium">{title}</h3>
      <div className="grid grid-cols-1 gap-2 mt-2">
        {children}
      </div>
    </div>
  );
}

interface KeyValueSectionProps {
  children: ReactNode;
}

export function KeyValueSection({ children }: KeyValueSectionProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {children}
    </div>
  );
}

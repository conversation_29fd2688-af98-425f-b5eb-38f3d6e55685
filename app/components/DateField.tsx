import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

import { useRemixFormContext } from "remix-hook-form";
import { cn } from "~/lib/utils";
import { Button } from "./ui/button";
import { Calendar } from "./ui/calendar";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";

export function DateField({
  name,
  label,
  description,
  placeholder = "Pick a date...",
  onChange,
}: {
  name: string;
  label?: string;
  description?: string;
  placeholder?: string;
  onChange?: (date: Date | undefined) => void;
}) {
  const form = useRemixFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem>
          {label && <FormLabel>{label}</FormLabel>}
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-[240px] pl-3 text-left font-normal",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {field.value ? (
                    format(field.value, "PPP")
                  ) : (
                    <span>{placeholder}</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                captionLayout="dropdown"
                selected={field.value}
                onSelect={(day, selectedDate, activeModifier, e) => {
                  field.onChange(day, selectedDate, activeModifier, e);
                  onChange?.(day);
                }}
                disabled={(date) =>
                  date > new Date() || date < new Date("1900-01-01")
                }
                defaultMonth={field.value}
              />
            </PopoverContent>
          </Popover>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}


import type { ReactNode } from 'react';
import { useRemixFormContext } from 'remix-hook-form';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from './ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

export type SelectFieldProps = {
  placeholder?: string;
  label?: string;
  description?: ReactNode;
  disabled?: boolean;
  name: string;
  options: { value: string; label: string }[];
  className?: string;
};

export function SelectField({ placeholder, label, description, options, name, disabled, className }: SelectFieldProps) {
  const form = useRemixFormContext();

  return (
    <FormField
      control={form.control}
      name={name}
      disabled={disabled}
      render={({ field: { onChange, value, ref, ...field } }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={onChange} defaultValue={value} {...field}>
            <FormControl>
              <SelectTrigger className={className}>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent ref={ref}>
              {options.map(val => (
                <SelectItem key={val.value} value={val.value}>
                  {val.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { formatDate } from "~/lib/utils";
import type { Route } from "./+types/route";
import { loader } from "./loader";

export { loader };

export const handle = {
  title: "Orders Information",
};

export default function SeminarianOrdersRoute({
  loaderData: { orders },
}: Route.ComponentProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Orders</CardTitle>
      </CardHeader>
      <CardContent>
        {orders.length === 0 ? (
          <div className="text-muted-foreground">No ordination records available.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Bishop</TableHead>
                <TableHead>Place</TableHead>
                <TableHead>Titulum</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>{order.orderName}</TableCell>
                  <TableCell>
                    {order.ordination?.date ? formatDate(new Date(order.ordination.date)) : "N/A"}
                  </TableCell>
                  <TableCell>{order.ordination?.bishop || "N/A"}</TableCell>
                  <TableCell>
                    {order.ordination?.placeName
                      ? `${order.ordination.placeName}${order.ordination.place ? `, ${order.ordination.place}` : ""}`
                      : order.ordination?.place || "N/A"}
                  </TableCell>
                  <TableCell>{order.titulum || "N/A"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}

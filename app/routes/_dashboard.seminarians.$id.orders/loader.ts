import { eq } from "drizzle-orm";
import { database } from "~/database/context";
import { orders } from "~/database/schema";
import type { Route } from "./+types/route";

export async function loader({ params }: Route.LoaderArgs) {
  const db = database();
  const seminarianId = parseInt(params.id, 10);

  if (isNaN(seminarianId)) {
    throw new Response("Invalid seminarian ID", { status: 400 });
  }

  // Get orders with ordination information
  const ordersData = await db.query.orders.findMany({
    where: eq(orders.seminarianId, seminarianId),
    with: {
      ordination: true,
    },
    orderBy: (orders, { asc }) => [asc(orders.orderName)],
  });

  return {
    orders: ordersData,
  };
}

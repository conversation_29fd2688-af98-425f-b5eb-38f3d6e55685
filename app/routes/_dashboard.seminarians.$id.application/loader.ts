import { eq } from "drizzle-orm";
import { database } from "~/database/context";
import { applications } from "~/database/schema";
import type { Route } from "./+types/route";

export async function loader({ params }: Route.LoaderArgs) {
  const db = database();
  const seminarianId = parseInt(params.id, 10);

  if (isNaN(seminarianId)) {
    throw new Response("Invalid seminarian ID", { status: 400 });
  }

  // Get application information
  const applicationData = await db.query.applications.findFirst({
    where: eq(applications.seminarianId, seminarianId),
  });

  return {
    application: applicationData,
  };
}

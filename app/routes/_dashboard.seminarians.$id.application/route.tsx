import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "~/components/ui/card";
import { KeyValueGroup, KeyValuePair, KeyValueSection } from "~/components/KeyValuePair";
import { formatDate } from "~/lib/utils";
import type { Route } from "./+types/route";
import { loader } from "./loader";

export { loader };

export const handle = {
  title: "Application Information",
};

export default function SeminarianApplicationRoute({
  loaderData: { application },
}: Route.ComponentProps) {
  if (!application) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Application Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground">No application information available.</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Application Information</CardTitle>
      </CardHeader>
      <CardContent>
        <KeyValueSection>
          <div className="space-y-4">
            <KeyValueGroup title="Application Dates">
              <KeyValuePair
                label="Application Entered"
                value={application.applicationEntered ? formatDate(new Date(application.applicationEntered)) : "N/A"}
              />
              <KeyValuePair
                label="Acceptance Sent"
                value={application.acceptanceSent ? formatDate(new Date(application.acceptanceSent)) : "N/A"}
              />
              <KeyValuePair
                label="Perpetual Engagement"
                value={application.perpetualEngagement ? formatDate(new Date(application.perpetualEngagement)) : "N/A"}
              />
            </KeyValueGroup>

            <KeyValueGroup title="Required Documents">
              <KeyValuePair label="Baptismal Certificate" value={application.baptismalCertificate ? "Yes" : "No"} />
              <KeyValuePair label="Confirmation Certificate" value={application.confirmationCertificate ? "Yes" : "No"} />
              <KeyValuePair label="Catholic Marriage" value={application.catholicMarriage ? "Yes" : "No"} />
              <KeyValuePair label="Marriage Certificate" value={application.marriageCertificate ? "Yes" : "No"} />
              <KeyValuePair label="Academic Transcripts" value={application.academicTranscripts ? "Yes" : "No"} />
              <KeyValuePair label="Reference Letter" value={application.referenceLetter ? "Yes" : "No"} />
              {application.referenceLetterFrom && (
                <KeyValuePair label="Reference Letter From" value={application.referenceLetterFrom} />
              )}
            </KeyValueGroup>
          </div>

          <div className="space-y-4">
            <KeyValueGroup title="Background">
              <KeyValuePair label="SSPX School" value={application.sspxSchool || "N/A"} />
              <KeyValuePair label="Skills" value={application.skills || "N/A"} />
              <KeyValuePair label="Musical Ability" value={application.musicalAbility || "N/A"} />
            </KeyValueGroup>
          </div>

          {(application.applicationNotes || application.notes) && (
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-lg font-medium">Notes</h3>
              {application.applicationNotes && (
                <div className="mt-2">
                  <h4 className="font-medium">Application Notes</h4>
                  <p className="whitespace-pre-wrap">{application.applicationNotes}</p>
                </div>
              )}
              {application.notes && (
                <div className="mt-2">
                  <h4 className="font-medium">Other Notes</h4>
                  <p className="whitespace-pre-wrap">{application.notes}</p>
                </div>
              )}
            </div>
          )}
        </KeyValueSection>
      </CardContent>
    </Card>
  );
}

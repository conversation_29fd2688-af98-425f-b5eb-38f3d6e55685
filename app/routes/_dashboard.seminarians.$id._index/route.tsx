import { useEffect } from "react";
import { useRemixForm } from "remix-hook-form";
import { DateField } from "~/components/DateField";
import FormSubmitBar from "~/components/FormSubmitBar";
import { KeyValuePair } from "~/components/KeyValuePair";
import { SelectField } from "~/components/SelectField";
import TextareaField from "~/components/TextareaField";
import TextField from "~/components/TextField";
import { Card, CardContent, CardHeader } from "~/components/ui/card";
import { Form } from "~/components/ui/form";
import { COUNTRY_OPTIONS } from "~/constants/countries";
import { calculateAge, sanitizeDefaultValues } from "~/lib/utils";
import type { Route } from "./+types/route";
import { action } from "./action";
import { loader } from "./loader";
import { insertResolver, updateResolver, type UpdateSchema } from "./resolver";

export { action, loader };

export const handle = {
  title: "Basic Information",
};

export default function SeminarianBasicInfoRoute({
  loaderData: { seminarian },
  params: { id }
}: Route.ComponentProps) {
  const isNew = id == 'new';
  const form = useRemixForm<UpdateSchema>({
    mode: "onChange",
    resolver: isNew ? insertResolver : updateResolver,
    defaultValues: sanitizeDefaultValues(seminarian),
  });

  useEffect(() => {if(!isNew)form.reset(sanitizeDefaultValues(seminarian))}, [seminarian]);

  return (
    <>
      <Form
        form={form}
        id="page-form"
        className="grid grid-cols-1 md:grid-cols-[2fr_1fr] gap-4"
      >
        <FormSubmitBar isNew={isNew}/>
        <div className="space-y-4">
          <Card>
            <CardHeader>Personal Information</CardHeader>
            <CardContent className="space-y-4">
              <div className="grid sm:grid-cols-3 gap-4">
                <TextField name="firstName" label="First Name" />
                <TextField name="middleName" label="Middle Name" />
                <TextField name="lastName" label="Last Name" />
              </div>
              <DateField
                name="dob"
                label={`Date of Birth${seminarian?.dob ? ` (age ${calculateAge(new Date(seminarian.dob))})` : ""}`}
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>Address</CardHeader>
            <CardContent className="space-y-4">
              <div className="grid sm:grid-cols-2 gap-4">
                <TextField name="street1" label="Street 1" />
                <TextField name="street2" label="Street 2" />
              </div>
              <div className="grid sm:grid-cols-3 gap-4">
                <TextField name="city" label="City" />
                <TextField name="zip" label="ZIP" />
                <TextField name="state" label="State" />
              </div>
              <div className="grid sm:grid-cols-2 gap-4">
                <SelectField
                  className="w-full"
                  name="country"
                  label="Country"
                  options={COUNTRY_OPTIONS}
                />
                <TextField name="diocese" label="Diocese" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>Notes</CardHeader>
            <CardContent>
              <TextareaField name="notes" />
            </CardContent>
          </Card>
        </div>
        <div className="space-y-4">
          <Card>
            <CardHeader>Details</CardHeader>
            <CardContent className="">
              <KeyValuePair label="Status" value={seminarian.status || "N/A"} />
              <KeyValuePair
                label="Seminary Year"
                value={seminarian.seminaryYear || "N/A"}
              />
              <KeyValuePair
                label="Semester"
                value={seminarian.semester || "N/A"}
              />
              <KeyValuePair
                label="Room #"
                value={seminarian.roomNumber || "N/A"}
              />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>Contact Information</CardHeader>
            <CardContent className="space-y-4">
              <TextField name="email" label="Email" />
              <TextField name="phone" label="Phone" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader>Nationality</CardHeader>
            <CardContent className="space-y-4">
              <SelectField
                className="w-full"
                name="citizenship"
                label="Citizenship"
                options={COUNTRY_OPTIONS}
              />
              <SelectField
                className="w-full"
                name="birthCountry"
                label="Birth country"
                options={COUNTRY_OPTIONS}
              />
              <TextField name="birthState" label="Birth State" />
              <TextField name="birthCity" label="Birth City" />
            </CardContent>
          </Card>
        </div>
      </Form>
    </>
  );
}

import { eq } from "drizzle-orm";
import { getValidatedFormData } from "remix-hook-form";
import { database } from "~/database/context";
import { seminarians } from "~/database/schema";
import type { Route } from "./+types/route";
import {
  insertResolver,
  updateResolver,
  type InsertSchema,
  type UpdateSchema,
} from "./resolver";
import { redirect } from "react-router";

export async function action({ request, params: { id } }: Route.ActionArgs) {
  const isNew = id == 'new';
  const { data, errors } =
    isNew
      ? await getValidatedFormData<InsertSchema>(request, insertResolver)
      : await getValidatedFormData<UpdateSchema>(request, updateResolver);

  if (errors) return { errors };

  const db = database();

  if (isNew) {
    const insertResult = await db.insert(seminarians).values(data as InsertSchema);
    return redirect(`/seminarians/${insertResult.lastInsertRowid}`);
  } else {
    await db.update(seminarians).set(data as UpdateSchema).where(eq(seminarians.id, parseInt(id)));
  }

  return null;
}

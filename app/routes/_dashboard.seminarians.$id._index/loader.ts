import { eq, type InferSelectModel } from "drizzle-orm";
import { database } from "~/database/context";
import { seminarians } from "~/database/schema";
import type { Route } from "./+types/route";

export async function loader({ params: { id } }: Route.LoaderArgs): Promise<{ seminarian: Partial<InferSelectModel<typeof seminarians>> }> {
  if(id == 'new') {
    return {
      seminarian: {},
    }
  }

  const db = database();
  const seminarianId = parseInt(id, 10);

  const seminarianData = await db.query.seminarians.findFirst({
    where: eq(seminarians.id, seminarianId),
  });

  return {
    seminarian: seminarianData!,
  };
}

import type { z } from "zod/v4";
import { insertSeminarianSchema, updateSeminarianSchema } from "~/database/schema";
import { zodResolverV4 } from "~/lib/zod-resolver-v4";

export type UpdateSchema = z.infer<typeof updateSeminarianSchema>;
export const updateResolver = zodResolverV4(updateSeminarianSchema);

export type InsertSchema = z.infer<typeof insertSeminarianSchema>;
export const insertResolver = zodResolverV4(insertSeminarianSchema);
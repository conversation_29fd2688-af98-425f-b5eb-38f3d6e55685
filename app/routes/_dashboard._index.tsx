import type { users } from "~/database/schema";
import type { Route } from "./+types/_dashboard._index";

export const handle = {
  title: "Dashboard",
};

export default function DashboardIndex({ matches }: Route.ComponentProps) {
  const { user } = matches.find(
    (match) => match && match.id === "routes/_dashboard"
  )?.data as { user: typeof users.$inferSelect };

  return (
    <div className="p-4 w-full max-w-3xl mx-auto bg-muted rounded-lg">
      <h1>Dashboard</h1>
      <p>Welcome, {user.username}</p>
    </div>
  );
}

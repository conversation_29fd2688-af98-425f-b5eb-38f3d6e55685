import { eq } from "drizzle-orm";
import { database } from "~/database/context";
import { roomAssignments } from "~/database/schema";
import type { Route } from "./+types/route";

export async function loader({ params }: Route.LoaderArgs) {
  const db = database();
  const seminarianId = parseInt(params.id, 10);

  if (isNaN(seminarianId)) {
    throw new Response("Invalid seminarian ID", { status: 400 });
  }

  // Get room assignments
  const roomAssignmentsData = await db.query.roomAssignments.findMany({
    where: eq(roomAssignments.seminarianId, seminarianId),
    orderBy: (roomAssignments, { desc }) => [desc(roomAssignments.assignedDate)],
  });

  return {
    roomAssignments: roomAssignmentsData,
  };
}

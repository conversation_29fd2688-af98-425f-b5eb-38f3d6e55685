import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { formatDate } from "~/lib/utils";
import type { Route } from "./+types/route";
import { loader } from "./loader";

export { loader };

export const handle = {
  title: "Housing Information",
};

export default function SeminarianHousingRoute({
  loaderData: { roomAssignments },
}: Route.ComponentProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Room Assignments</CardTitle>
      </CardHeader>
      <CardContent>
        {roomAssignments.length === 0 ? (
          <div className="text-muted-foreground">No room assignment records available.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Room Number</TableHead>
                <TableHead>Assigned Date</TableHead>
                <TableHead>Vacated Date</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {roomAssignments.map((assignment) => (
                <TableRow key={assignment.id}>
                  <TableCell>{assignment.roomNumber || "N/A"}</TableCell>
                  <TableCell>{formatDate(new Date(assignment.assignedDate))}</TableCell>
                  <TableCell>
                    {assignment.vacatedDate ? formatDate(new Date(assignment.vacatedDate)) : "Current"}
                  </TableCell>
                  <TableCell>{assignment.notes || "N/A"}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}

import { useAutoAnimate } from "@formkit/auto-animate/react";
import { ArrowLeft } from "lucide-react";
import { Link, Outlet, useLocation, useParams } from "react-router";
import { Button } from "~/components/ui/button";
import { cn } from "~/lib/utils";
import type { Route } from "./+types/route";
import { loader } from "./loader";

export { loader };

function SeminarianTabLink({ tab, label, disabled }: { tab: string; label: string; disabled?: boolean; }) {
  const location = useLocation();
  const { id } = useParams();
  const pathname = `/seminarians/${id}${tab === "basic" ? "/" : `/${tab}`}`;
  const isActive = location.pathname == pathname;

  return (
    <Link
      prefetch="intent"
      to={pathname}
      aria-disabled={disabled}
      className={cn(
        "py-2 px-1 border-b-2 font-medium text-sm",
        isActive
          ? "border-primary text-primary"
          : "border-transparent text-muted-foreground hover:text-foreground hover:border-border",
        disabled && "opacity-50 pointer-events-none"
      )}
    >
      {label}
    </Link>
  );
}

export default function SeminarianDetail({
  loaderData: { fullName },
  params: { id },
}: Route.ComponentProps) {
  const [animateRef] = useAutoAnimate();
  const isNew = id == 'new';

  return (
    <div className="p-4 sm:p-6 flex flex-col gap-6">
      <header className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" asChild>
            <Link to="/seminarians">
              <ArrowLeft className="h-4 w-4" />
            </Link>
          </Button>
          <h1 className="font-bold text-2xl">{fullName}</h1>
        </div>
      </header>

      <div ref={animateRef} className="w-full">
        <div className="border-b mb-6">
          <nav className="flex gap-4 -mb-px">
            <SeminarianTabLink tab="basic" label="Basic Info" />
            <SeminarianTabLink tab="application" label="Application" disabled={isNew} />
            <SeminarianTabLink tab="academic" label="Academic" disabled={isNew} />
            <SeminarianTabLink tab="housing" label="Housing" disabled={isNew} />
            <SeminarianTabLink tab="orders" label="Orders" disabled={isNew} />
            <SeminarianTabLink tab="departments" label="Departments" disabled={isNew} />
          </nav>
        </div>

        <Outlet />
      </div>
    </div>
  );
}

import { redirect } from "react-router";
import type { Route } from "./+types/login";

import { zodResolver } from "@hookform/resolvers/zod";
import { and, eq } from "drizzle-orm";
import type { FieldErrors } from "react-hook-form";
import { getValidatedFormData, useRemixForm } from "remix-hook-form";
import { z } from "zod";
import TextField from "~/components/TextField";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { database } from "~/database/context";
import { users } from "~/database/schema";
import { hashPassword } from "~/lib/utils";
import { commitSession, getSession } from "../sessions.server";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
const schema = z.object({
  username: z.string().min(1, "Username cannot be empty").max(255),
  password: z.string().min(1, "Password cannot be empty").max(255),
});
const resolver = zodResolver(schema);
type SchemaType = z.infer<typeof schema>;

export async function action({ request }: Route.ActionArgs) {
  const session = await getSession(request.headers.get("Cookie"));
  const { data, errors } = await getValidatedFormData<SchemaType>(
    request,
    resolver
  );

  if (errors) return { errors };

  const { username, password } = data;

  const passwordHash = await hashPassword(password);

  const db = database();
  const foundUser = await db.query.users.findFirst({
    where: and(
      eq(users.username, username),
      eq(users.passwordHash, passwordHash),
      eq(users.isActive, true)
    ),
  });

  if (foundUser == null) {
    return {
      errors: {
        root: {
          message: "Invalid username or password",
          type: "invalid_credentials",
        },
      } as FieldErrors<SchemaType>,
    };
  }

  session.set("userId", foundUser.id);

  const { searchParams } = new URL(request.url);
  const returnTo = searchParams.get("return_to") || "/";
  return redirect(returnTo.startsWith("/") ? returnTo : "/", {
    headers: {
      "Set-Cookie": await commitSession(session),
    },
  });
}

export default function Login() {
  const form = useRemixForm<SchemaType>({
    mode: "onSubmit",
    resolver,
  });

  return (
    <main className="w-full h-dvh flex justify-center items-center p-6">
      <Card className="w-full max-w-xs">
        <CardHeader>
          <CardTitle className="text-2xl">Login</CardTitle>
          <CardDescription>
            Enter your username and password below to login to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form form={form} className="flex flex-col gap-8">
            {form.formState.errors.root ? (
              <p className="text-destructive">
                {form.formState.errors.root?.message}
              </p>
            ) : null}
            <TextField
              type="text"
              name="username"
              label="Username"
              autoComplete="username"
              className="w-full"
            />
            <TextField
              type="password"
              name="password"
              label="Password"
              autoComplete="current-password"
              className="w-full"
            />
            <Button type="submit">Login</Button>
          </Form>
        </CardContent>
      </Card>
    </main>
  );
}

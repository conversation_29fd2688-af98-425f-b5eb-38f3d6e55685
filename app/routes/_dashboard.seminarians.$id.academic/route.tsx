import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { formatDate } from "~/lib/utils";
import type { Route } from "./+types/route";
import { loader } from "./loader";

export { loader };

export const handle = {
  title: "Academic Information",
};

export default function SeminarianAcademicRoute({
  loaderData: { enrollments, grades },
}: Route.ComponentProps) {
  return (
    <div className="grid gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Enrollments</CardTitle>
        </CardHeader>
        <CardContent>
          {enrollments.length === 0 ? (
            <div className="text-muted-foreground">No enrollment records available.</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Academic Year</TableHead>
                  <TableHead>Seminary Year</TableHead>
                  <TableHead>Semester</TableHead>
                  <TableHead>Entry Date</TableHead>
                  <TableHead>Notes</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {enrollments.map((enrollment) => (
                  <TableRow key={enrollment.id}>
                    <TableCell>{enrollment.academicYear || "N/A"}</TableCell>
                    <TableCell>{enrollment.seminaryYear || "N/A"}</TableCell>
                    <TableCell>{enrollment.semester || "N/A"}</TableCell>
                    <TableCell>{formatDate(new Date(enrollment.entryDate))}</TableCell>
                    <TableCell>{enrollment.notes || "N/A"}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Grades</CardTitle>
        </CardHeader>
        <CardContent>
          {grades.length === 0 ? (
            <div className="text-muted-foreground">No grade records available.</div>
          ) : (
            <div className="space-y-6">
              {(() => {
                // Group grades by academic year and semester
                const groupedGrades = grades.reduce((acc, grade) => {
                  const key = `${grade.courseOffering.academicYear}-${grade.courseOffering.semester}`;
                  if (!acc[key]) {
                    acc[key] = {
                      academicYear: grade.courseOffering.academicYear,
                      semester: grade.courseOffering.semester,
                      grades: [],
                    };
                  }
                  acc[key].grades.push(grade);
                  return acc;
                }, {} as Record<string, { academicYear: number; semester: string; grades: typeof grades }>)

                // Sort by academic year (descending) and semester (Fall before Spring)
                const sortedTerms = Object.values(groupedGrades).sort((a, b) => {
                  if (a.academicYear !== b.academicYear) {
                    return b.academicYear - a.academicYear;
                  }
                  return a.semester === "Fall" ? -1 : 1;
                });

                return sortedTerms.map((term) => (
                  <div key={`${term.academicYear}-${term.semester}`} className="space-y-2">
                    <h3 className="text-lg font-medium">
                      {term.academicYear}-{term.academicYear + 1} {term.semester}
                    </h3>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Course</TableHead>
                          <TableHead>Instructor</TableHead>
                          <TableHead className="text-right">Credits Earned</TableHead>
                          <TableHead className="text-right">Grade (%)</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {term.grades.map((grade) => (
                          <TableRow key={grade.id}>
                            <TableCell>{grade.courseOffering.course.courseName}</TableCell>
                            <TableCell>
                              {grade.courseOffering.user.honorific
                                ? `${grade.courseOffering.user.honorific} ${grade.courseOffering.user.lastName}`
                                : [grade.courseOffering.user.firstName, grade.courseOffering.user.lastName]
                                    .filter(Boolean)
                                    .join(" ") || "N/A"}
                            </TableCell>
                            <TableCell className="text-right">{grade.creditsEarned || "N/A"}</TableCell>
                            <TableCell className="text-right">{grade.percentGrade !== null ? `${grade.percentGrade}%` : "N/A"}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ));
              })()}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

import { eq } from "drizzle-orm";
import { database } from "~/database/context";
import { enrollments, grades } from "~/database/schema";
import type { Route } from "./+types/route";

export async function loader({ params }: Route.LoaderArgs) {
  const db = database();
  const seminarianId = parseInt(params.id, 10);

  if (isNaN(seminarianId)) {
    throw new Response("Invalid seminarian ID", { status: 400 });
  }

  // Get enrollments
  const enrollmentsData = await db.query.enrollments.findMany({
    where: eq(enrollments.seminarianId, seminarianId),
    orderBy: (enrollments, { desc }) => [desc(enrollments.academicYear), desc(enrollments.entryDate)],
  });

  // Get grades with course information
  const gradesData = await db.query.grades.findMany({
    where: eq(grades.seminarianId, seminarianId),
    with: {
      courseOffering: {
        with: {
          course: true,
          user: true,
        },
      },
    },
  });

  return {
    enrollments: enrollmentsData,
    grades: gradesData,
  };
}

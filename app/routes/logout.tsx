import { redirect } from "react-router";
import type { Route } from "./+types/logout";

import { commitSession, getSession } from "../sessions.server";

export async function loader({ request }: Route.LoaderArgs) {
  const session = await getSession(request.headers.get("Cookie"));

  session.unset("userId");

  const { search } = new URL(request.url);
  return redirect("/login" + search, {
    headers: { "Set-Cookie": await commitSession(session) },
  });
}

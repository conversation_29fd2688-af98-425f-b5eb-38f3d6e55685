import { eq } from "drizzle-orm";
import { Outlet, redirect, unstable_createContext } from "react-router";
import { Separator } from "~/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/sidebar";
import { database } from "~/database/context";
import { users } from "~/database/schema";
import { getSession, type SessionData } from "~/sessions.server";
import type { Route } from "./+types/route";
import DashboardSidebar from "./components/DashboardSidebar";
import RouteBreadcrumbTrail from "./components/RouteBreadcrumb";
import PageInfiniteLoader from "~/components/PageInfiniteLoader";
import { useAutoAnimate } from "@formkit/auto-animate/react";

export const sessionContext = unstable_createContext<SessionData>();

const sessionMiddleware: Route.unstable_MiddlewareFunction = async ({
  context,
  request,
}) => {
  let session = await getSession(request.headers.get("Cookie"));
  context.set(sessionContext, session.data as SessionData);
};

export const userContext = unstable_createContext<typeof users.$inferSelect>();

const userAuthentication: Route.unstable_MiddlewareFunction = async ({
  request,
  context,
}) => {
  const session = context.get(sessionContext);

  const db = database();
  const user = await db.query.users.findFirst({
    where: eq(users.id, session.userId),
  });

  if (!user?.isActive) {
    const { pathname, search, hash } = new URL(request.url);
    const searchParams = new URLSearchParams({
      return_to: pathname + search + hash,
    });
    throw redirect(`/login?${searchParams.toString()}`);
  }

  context.set(userContext, user);
};

export const unstable_middleware = [sessionMiddleware, userAuthentication];

export async function loader({ request, context }: Route.LoaderArgs) {
  const user = context.get(userContext);

  return {
    user,
  };
}

export default function DashboardLayout() {
  const [animateRef] = useAutoAnimate();

  return (
    <>
      <SidebarProvider>
        <DashboardSidebar />
        <SidebarInset>
          <header className="flex h-12 shrink-0 items-center gap-2 border-b p-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2" />
            <RouteBreadcrumbTrail />
          </header>
          <div ref={animateRef} className="w-full">
            <Outlet />
          </div>
        </SidebarInset>
      </SidebarProvider>
      <PageInfiniteLoader />
    </>
  );
}

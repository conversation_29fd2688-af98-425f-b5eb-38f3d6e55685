import React from "react";
import { Link, useLocation, useMatches } from "react-router";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb";

export default function RouteBreadcrumbTrail() {
  const { pathname } = useLocation();
  const matches = useMatches();

  const breadcrumbItems = matches.filter(
    (match) => (match.handle as any)?.title as string
  );
  
  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={item.pathname}>
            <BreadcrumbItem className="hidden md:block">
              {item.pathname != pathname ? (
                <BreadcrumbLink asChild>
                  <Link to={item.pathname}>
                    {(item.handle as any)?.title as string}
                  </Link>
                </BreadcrumbLink>
              ) : (
                <BreadcrumbPage>
                  {(item.handle as any)?.title as string}
                </BreadcrumbPage>
              )}
            </BreadcrumbItem>
            {index < breadcrumbItems.length - 1 && (
              <BreadcrumbSeparator className="hidden md:block" />
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

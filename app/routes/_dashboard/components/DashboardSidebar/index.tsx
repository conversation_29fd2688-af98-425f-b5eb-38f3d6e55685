import {
  Book<PERSON>pen,
  BookOpenText,
  Home,
  LogOut,
  Settings,
  UsersRound,
} from "lucide-react";
import logo from "/logo.svg";
import type { ReactNode } from "react";
import { Link, useLocation } from "react-router";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
} from "~/components/ui/sidebar";
import { useDashboardLoader } from "../../hooks/use-dashboard-loader";

interface SidebarLinkProps {
  to: string;
  icon?: ReactNode;
  label: string;
}

function SidebarLink({ to, icon, label }: SidebarLinkProps) {
  const { pathname } = useLocation();
  return (
    <SidebarMenuItem>
      <SidebarMenuButton asChild isActive={pathname === to}>
        <Link to={to}>
          {icon} {label}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
}

const SIDEBAR_LINKS: SidebarLinkProps[] = [
  {
    to: "/",
    icon: <Home />,
    label: "Dashboard",
  },
  {
    to: "/seminarians",
    icon: <UsersRound />,
    label: "Seminarians",
  },
  {
    to: "/courses",
    icon: <BookOpenText />,
    label: "Courses",
  },
];

export default function DashboardSidebar() {
  const { user } = useDashboardLoader();

  return (
    <Sidebar>
      <SidebarHeader>
        <img src={logo} alt="STAS Seminarian Registry" className="w-full" />
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {SIDEBAR_LINKS.map((props) => (
                <SidebarLink key={props.to} {...props} />
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarLink to="/settings" icon={<Settings />} label="Settings" />
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link to="/logout" prefetch="none">
                Logout{" "}
                <SidebarMenuAction className="cursor-pointer">
                  <LogOut />
                </SidebarMenuAction>
              </Link>
            </SidebarMenuButton>
            <h3 className="text-muted-foreground">
              You are logged in as <b>{user?.username}</b>
            </h3>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}

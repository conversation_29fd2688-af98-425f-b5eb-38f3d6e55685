import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import type { Route } from "./+types/route";
import { loader } from "./loader";

export { loader };

export const handle = {
  title: "Department Assignments",
};

export default function SeminarianDepartmentsRoute({
  loaderData: { departmentAssignments },
}: Route.ComponentProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Department Assignments</CardTitle>
      </CardHeader>
      <CardContent>
        {departmentAssignments.length === 0 ? (
          <div className="text-muted-foreground">No department assignments available.</div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Department</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Prefect</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {departmentAssignments.map((assignment) => (
                <TableRow key={`${assignment.seminarianId}-${assignment.departmentId}`}>
                  <TableCell>{assignment.department.name}</TableCell>
                  <TableCell>{assignment.department.category || "N/A"}</TableCell>
                  <TableCell>
                    {assignment.department.prefect
                      ? assignment.department.prefect.honorific
                        ? `${assignment.department.prefect.honorific} ${assignment.department.prefect.lastName}`
                        : [assignment.department.prefect.firstName, assignment.department.prefect.lastName]
                            .filter(Boolean)
                            .join(" ")
                      : "N/A"}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}

import { eq } from "drizzle-orm";
import { database } from "~/database/context";
import { departmentAssignments } from "~/database/schema";
import type { Route } from "./+types/route";

export async function loader({ params }: Route.LoaderArgs) {
  const db = database();
  const seminarianId = parseInt(params.id, 10);

  if (isNaN(seminarianId)) {
    throw new Response("Invalid seminarian ID", { status: 400 });
  }

  // Get department assignments with department information
  const departmentAssignmentsData = await db.query.departmentAssignments.findMany({
    where: eq(departmentAssignments.seminarianId, seminarianId),
    with: {
      department: {
        with: {
          prefect: true,
        },
      },
    },
  });

  return {
    departmentAssignments: departmentAssignmentsData,
  };
}

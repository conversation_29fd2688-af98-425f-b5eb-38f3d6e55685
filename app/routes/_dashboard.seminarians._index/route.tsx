import { Plus } from "lucide-react";
import { <PERSON> } from "react-router";
import { But<PERSON> } from "~/components/ui/button";
import type { Route } from "./+types/route";
import { SeminariansTable } from "./components/SeminariansTable";
import { loader } from "./loader";

export { loader };

export default function SeminariansIndex({
  loaderData: { seminarians },
}: Route.ComponentProps) {
  return (
    <div className="p-4 sm:p-6 flex flex-col gap-6">
      <header className="flex justify-between">
        <h1 className="font-bold text-2xl">Seminarians</h1>
        <div className="flex gap-4">
          <Button asChild>
            <Link to="new">
              <Plus /> New seminarian
            </Link>
          </Button>
        </div>
      </header>
      <div className="flex flex-col gap-4">
        <SeminariansTable seminarians={seminarians || []} />
      </div>
    </div>
  );
}

import { Button } from "~/components/ui/button";
import type { SeminarianTableView } from "../types";
import type {
  ColumnDef,
  HeaderContext,
  RowSelectionState,
} from "@tanstack/react-table";
import { ArrowDown, ArrowUp, ArrowUpDown } from "lucide-react";
import {
  DataTable,
  SELECT_COLUMN,
  type DataTableActions,
} from "~/components/DataTable";
import { useMemo, useState, type FC } from "react";
import { Link } from "react-router";
import { defaultLimit } from "../resolver";

function createTableHeader(
  label: string
): FC<HeaderContext<SeminarianTableView, unknown>> {
  return ({ column }: HeaderContext<SeminarianTableView, unknown>) => {
    const sort = column.getIsSorted();

    return (
      <Button
        variant="link"
        onClick={() => column.toggleSorting(sort === "asc")}
        className="!p-0"
      >
        {label}
        {!sort ? (
          <ArrowUpDown className="ml-2 h-4 w-4" />
        ) : sort === "asc" ? (
          <ArrowUp className="ml-2 h-4 w-4" />
        ) : (
          <ArrowDown className="ml-2 h-4 w-4" />
        )}
      </Button>
    );
  };
}

export const seminariansTableColumns: ColumnDef<SeminarianTableView>[] = [
  {
    header: createTableHeader("Name"),
    accessorKey: "name",
    cell: ({ row }) => (
      <Link
        prefetch="intent"
        to={row.original.id.toString() + "/"}
        className="font-semibold"
      >
        {row.original.name}
      </Link>
    ),
  },
  {
    header: createTableHeader("Year"),
    accessorKey: "year",
  },
  {
    header: createTableHeader("Phone"),
    accessorKey: "phone",
  },
  {
    header: createTableHeader("Email"),
    accessorKey: "email",
  },
];

export function SeminariansTable({
  seminarians,
}: {
  seminarians: SeminarianTableView[];
}) {
  const [selection, setSelection] = useState<RowSelectionState>({});

  const hasSelections = useMemo(
    () => Object.keys(selection).length > 0,
    [selection]
  );

  const actions: DataTableActions[] = [];

  if (hasSelections) {
    actions.push({
      label: <span className="text-destructive">Delete</span>,
      onClick: () => {
        alert("Not implemented yet! " + JSON.stringify(selection));
      },
    });
  }

  return (
    <DataTable
      defaultPageSize={defaultLimit}
      columns={seminariansTableColumns}
      data={seminarians}
      selection={selection}
      setSelection={setSelection}
      idKey="id"
      actions={actions}
    />
  );
}

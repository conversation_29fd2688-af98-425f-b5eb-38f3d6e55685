import { relations, Table } from "drizzle-orm";
import {
  integer,
  primaryKey,
  sqliteTable,
  text,
  unique,
} from "drizzle-orm/sqlite-core";
import {
  createInsertSchema,
  createSelectSchema,
  createUpdateSchema,
  type BuildRefine,
} from "drizzle-zod";
import { z } from "zod/v4"; // Import zod for potential customizations

// --- Enums (as text constraints) ---
export const statusEnumValues = [
  "Application Declined",
  "Applicant",
  "Enrolled",
  "Year of Apostolate",
  "OnLeave",
  "Departed",
  "Deacon Sent to District",
  "Graduated",
  "Deceased",
] as const; // Use 'as const' for type safety

export const semesterEnumValues = ["Fall", "Spring"] as const;

export const seminaryYearEnumValues = [
  "Humanities",
  "Spirituality",
  "Philosophy I",
  "Philosophy II",
  "Theology I",
  "Theology II",
  "Theology III",
] as const;

export const ordinationOrderEnumValues = [
  "Tonsure",
  "<PERSON>",
  "Lector",
  "Exorcist",
  "Acolyte",
  "Subdiaconate",
  "Diaconate",
  "Priesthood",
] as const;

// New enum for Department Category
export const departmentCategoryEnumValues = [
  "Liturgy",
  "General",
  "Communications",
  "Maintenance",
] as const;

export const honorificEnumValues = [
  "Mr.",
  "Prof.",
  "Dr.",
  "Dcn.",
  "Fr.",
  "Bp.",
  "Mrs.",
  "Ms.",
  "Sr.",
] as const;

// --- Tables ---

export const users = sqliteTable("users", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  email: text("email"),
  phone: text("phone"),
  honorific: text("honorific", { enum: honorificEnumValues }),
  firstName: text("first_name"),
  lastName: text("last_name"),
  username: text("username").notNull().unique(),
  passwordHash: text("password_hash").notNull(),
  isActive: integer("is_active", { mode: "boolean" }).default(true).notNull(),
});

export const userActivity = sqliteTable("user_activity", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  userId: integer("user_id", { mode: "number" })
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),
  old: text("old", { mode: "json" }),
  new: text("new", { mode: "json" }),
  action: text("action").notNull(),
  resourceType: text("resource_type"),
  resourceId: text("resource_id"),
  note: text("note"),
  timestamp: integer("timestamp", { mode: "timestamp" })
    .notNull(),
});

/** SEMINARIAN TABLES */
export const seminarians = sqliteTable("seminarians", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  lastName: text("last_name").notNull(),
  firstName: text("first_name"),
  middleName: text("middle_name"),
  street1: text("street1"),
  street2: text("street2"),
  city: text("city"),
  state: text("state", { length: 2 }),
  zip: text("zip"),
  country: text("country", { length: 3 }),
  mailingStreet: text("mailing_street"),
  phone: text("phone"),
  email: text("email").unique(), // Added unique constraint back based on previous index
  citizenship: text("citizenship"),
  diocese: text("diocese"),
  dob: integer("dob", { mode: "timestamp" }), // Store dates as Unix timestamps
  birthCity: text("birth_city"),
  birthState: text("birth_state", { length: 2 }),
  birthCountry: text("birth_country", { length: 3 }),
  //States
  status: text("status", { enum: statusEnumValues })
    .notNull()
    .default("Applicant"),
  seminaryYear: text("seminary_year", { enum: seminaryYearEnumValues }),
  classYear: integer("class_year", { mode: "number" }),
  roomNumber: integer("room_number"),
  notes: text("notes"),
});

export const applications = sqliteTable("applications", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  seminarianId: integer("seminarian_id").references(() => seminarians.id, {
    onDelete: "set null",
  }),
  applicationEntered: integer("application_entered", { mode: "timestamp" }),
  acceptanceSent: integer("acceptance_sent", { mode: "timestamp" }),
  baptismalCertificate: integer("baptismal_certificate", {
    mode: "boolean",
  }).default(false),
  confirmationCertificate: integer("confirmation_certificate", {
    mode: "boolean",
  }).default(false),
  catholicMarriage: integer("catholic_marriage", { mode: "boolean" }).default(
    false
  ),
  marriageCertificate: integer("marriage_certificate", {
    mode: "boolean",
  }).default(false),
  academicTranscripts: integer("academic_transcripts", {
    mode: "boolean",
  }).default(false),
  referenceLetter: integer("reference_letter", { mode: "boolean" }).default(
    false
  ),
  referenceLetterFrom: text("reference_letter_from"),
  applicationNotes: text("application_notes"),
  sspxSchool: text("sspx_school"),
  perpetualEngagement: integer("perpetual_engagement", { mode: "timestamp" }),
  skills: text("skills"),
  musicalAbility: text("musical_ability"),
  notes: text("notes"),
});

export const courses = sqliteTable("courses", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  courseName: text("course_name").notNull(),
  credits: integer("credits").notNull(),
});

export const courseOfferings = sqliteTable("course_offerings", {
  //Professor defined in relationships database
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  courseId: integer("course_id", { mode: "number" })
    .notNull()
    .references(() => courses.id, { onDelete: "cascade" }),
  academicYear: integer("academic_year").notNull(),
  semester: text("semester", { enum: semesterEnumValues }).notNull(),
}, (t) => [unique().on(t.courseId, t.academicYear, t.semester)]);

export const courseEnrollments = sqliteTable("course_enrollments", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  seminarianId: integer("seminarian_id", { mode: "number" })
    .notNull()
    .references(() => seminarians.id, { onDelete: "cascade" }),
  courseOfferingId: integer("course_offering_id", { mode: "number" })
    .notNull()
    .references(() => courseOfferings.id, { onDelete: "cascade" }),
  creditsEarned: integer("credits_earned"),
  percentGrade: integer("percent_grade"),
  notes: text("notes"),
}, (t) => [unique().on(t.seminarianId, t.courseOfferingId)]);

export const ordinations = sqliteTable("ordinations", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  date: integer("date", { mode: "timestamp" }),
  bishop: text("bishop"),
  placeName: text("place_name"),
  place: text("place"),
  notes: text("notes"),
});

export const orders = sqliteTable("orders", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  ordinationId: integer("ordination_id", { mode: "number" }).references(
    () => ordinations.id,
    {
      onDelete: "cascade",
    }
  ),
  seminarianId: integer("seminarian_id", { mode: "number" }).references(
    () => seminarians.id,
    {
      onDelete: "cascade",
    }
  ),
  orderName: text("order_name", { enum: ordinationOrderEnumValues }),
  titulum: text("titulum"),// TODO: Figure out what this is?
});

// New departments table
export const departments = sqliteTable("departments", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  category: text("category", { enum: departmentCategoryEnumValues }),
  //Prefect defined in relationships database
  subPrefectSeminarianId: integer("sub_prefect_seminarian_id", { mode: "number" }).references(
    () => seminarians.id,
    {
      onDelete: "set null", // If seminarian is deleted, remove them as sub-prefect, keep department
    }
  ),
});

// New junction table for Department Assignments
export const departmentAssignments = sqliteTable(
  "department_assignments",
  {
    seminarianId: integer("seminarian_id", { mode: "number" })
      .notNull()
      .references(() => seminarians.id, { onDelete: "cascade" }),
    departmentId: integer("department_id", { mode: "number" })
      .notNull()
      .references(() => departments.id, { onDelete: "cascade" }),
  },
  (t) => [primaryKey({ columns: [t.seminarianId, t.departmentId] })]
);

// --- Relations ---
export const usersRelations = relations(users, ({ many }) => ({
  activity: many(userActivity),
}));

export const seminariansRelations = relations(seminarians, ({ one, many }) => ({
  documentFile: one(applications, {
    fields: [seminarians.id],
    references: [applications.seminarianId],
  }),
  courseEnrollments: many(courseEnrollments),
  orders: many(orders),
  departmentAssignments: many(departmentAssignments), // Add relation
}));

export const applicationsRelations = relations(applications, ({ one }) => ({
  seminarian: one(seminarians, {
    fields: [applications.seminarianId],
    references: [seminarians.id],
  }),
}));

export const coursesRelations = relations(courses, ({ many }) => ({
  courseOfferings: many(courseOfferings),
}));

export const courseOfferingsRelations = relations(
  courseOfferings,
  ({ one, many }) => ({
    course: one(courses, {
      fields: [courseOfferings.courseId],
      references: [courses.id],
    }),
    courseEnrollments: many(courseEnrollments),
  })
);

export const courseEnrollmentsRelations = relations(courseEnrollments, ({ one }) => ({
  seminarian: one(seminarians, {
    fields: [courseEnrollments.seminarianId],
    references: [seminarians.id],
  }),
  courseOffering: one(courseOfferings, {
    fields: [courseEnrollments.courseOfferingId],
    references: [courseOfferings.id],
  }),
}));

export const ordinationsRelations = relations(ordinations, ({ many }) => ({
  orders: many(orders),
}));

export const ordersRelations = relations(orders, ({ one }) => ({
  ordination: one(ordinations, {
    fields: [orders.ordinationId],
    references: [ordinations.id],
  }),
  seminarian: one(seminarians, {
    fields: [orders.seminarianId],
    references: [seminarians.id],
  }),
}));

// New relations for departments
export const departmentsRelations = relations(departments, ({ one, many }) => ({
  subPrefectSeminarian: one(seminarians, {
    fields: [departments.subPrefectSeminarianId],
    references: [seminarians.id],
  }),
  assignments: many(departmentAssignments), // Add relation
}));

// New relations for departmentAssignments
export const departmentAssignmentsRelations = relations(
  departmentAssignments,
  ({ one }) => ({
    seminarian: one(seminarians, {
      fields: [departmentAssignments.seminarianId],
      references: [seminarians.id],
    }),
    department: one(departments, {
      fields: [departmentAssignments.departmentId],
      references: [departments.id],
    }),
  })
);

// --- Zod Schemas (using drizzle-zod) ---
type Refinement<TTable extends Table> = BuildRefine<
  Pick<TTable["_"]["columns"], keyof TTable["$inferInsert"]>,
  Partial<Record<"bigint" | "boolean" | "date" | "number" | "string", true>>
>;

// Personnel
const userRefinements: Refinement<typeof users> = {
  id: z.undefined(),
  email: z.email().nullable(),
};
export const insertUserSchema = createInsertSchema(users, userRefinements);
export const selectUserSchema = createSelectSchema(users);

// Seminarians
const COUNTRY_CODE_SCHEMA = z
  .string()
  .length(3, "Must be a valid country")
  .toUpperCase();
const STATE_CODE_SCHEMA = z
  .string()
  .length(2, "Must be a valid 2-letter state code")
  .toUpperCase();
const ZIP_CODE_SCHEMA = z
  .string()
  .regex(/^[0-9]{5}$/, "Must be a valid 5-digit zip code");
const seminarianRefinements: Refinement<typeof seminarians> = {
  id: z.transform(() => undefined),
  email: z.email().nullable(),
  dob: z.coerce.date().optional().nullable(),
  zip: ZIP_CODE_SCHEMA.optional().nullable(),
  country: COUNTRY_CODE_SCHEMA.optional().nullable(),
  citizenship: COUNTRY_CODE_SCHEMA.optional().nullable(),
  birthCountry: COUNTRY_CODE_SCHEMA.optional().nullable(),
  state: STATE_CODE_SCHEMA.optional().nullable(),
  birthState: STATE_CODE_SCHEMA.optional().nullable(),
};
export const insertSeminarianSchema = createInsertSchema(
  seminarians,
  seminarianRefinements
);
export const updateSeminarianSchema = createUpdateSchema(
  seminarians,
  seminarianRefinements
);
export const selectSeminarianSchema = createSelectSchema(seminarians);

// DocumentFiles
export const insertDocumentFileSchema = createInsertSchema(applications);
export const selectDocumentFileSchema = createSelectSchema(applications);

// Courses
export const insertCourseSchema = createInsertSchema(courses);
export const selectCourseSchema = createSelectSchema(courses);

// CourseOfferings
export const insertCourseOfferingSchema = createInsertSchema(courseOfferings);
export const selectCourseOfferingSchema = createSelectSchema(courseOfferings);

// CourseEnrollments
export const insertGradeSchema = createInsertSchema(courseEnrollments);
export const selectGradeSchema = createSelectSchema(courseEnrollments);

// Ordinations
export const insertOrdinationSchema = createInsertSchema(ordinations);
export const selectOrdinationSchema = createSelectSchema(ordinations);

// Orders
export const insertOrderSchema = createInsertSchema(orders);
export const selectOrderSchema = createSelectSchema(orders);

// Departments
export const insertDepartmentSchema = createInsertSchema(departments);
export const selectDepartmentSchema = createSelectSchema(departments);

// DepartmentAssignments
export const insertDepartmentAssignmentSchema = createInsertSchema(
  departmentAssignments
);
export const selectDepartmentAssignmentSchema = createSelectSchema(
  departmentAssignments
);

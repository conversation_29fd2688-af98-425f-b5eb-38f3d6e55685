import { AsyncLocalStorage } from "node:async_hooks";

import type { BetterSQLite3Database } from "drizzle-orm/better-sqlite3";

import * as schema from "./schema";

export const DatabaseContext = new AsyncLocalStorage<
BetterSQLite3Database<typeof schema>
>();

export function database() {
  const db = DatabaseContext.getStore();
  if (!db) {
    throw new Error("DatabaseContext not set");
  }
  return db;
}

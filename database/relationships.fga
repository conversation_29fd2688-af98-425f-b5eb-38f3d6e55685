model
  schema 1.1

# Seminary Registry Authorization Model
# Based on the Drizzle schema for SSPX Seminary Registry
#
# This model defines authorization relationships for:
# - Users (admin, staff, professors) - the primary actors
# - Seminarians (data subjects, not users)
# - Departments (with user sub-prefects and seminarian members)
# - Academic records (courses, enrollments, grades)
# - Applications and ordination records
# - User activity tracking

type user
  relations
    define editor: [seminary#admin]
    define viewer: [seminary#staff]

type seminary
  relations
    define admin: [user]
    define staff: [user] or admin
    define secretary: [user] or staff
    define professor: [course#professor] 

type seminarian
  relations
    define student_of_seminary: [seminary]
    define editor: staff from student_of_seminary

type department
  relations
    define prefect: [user]
    define in_seminary: [seminary]
    define editor: prefect or staff from in_seminary

type application
  relations
    define of_seminarian: [seminarian]
    define editor: editor from of_seminarian

type course
  relations
    define offered_at: [seminary]
    define professor: [course_offering#professor]
    define editor: staff from offered_at

type course_offering
  relations
    define offering_course: [course]
    define professor: [user]
    define grader: [user] or professor
    define editor: professor or editor from offering_course
    define student: [course_enrollment#student]

type course_enrollment
  relations
    define taking_course_offering: [course_offering]
    define student: [seminarian]
    define professor: professor from taking_course_offering
    define editor: professor or editor from student
    define grader: grader from taking_course_offering

type ordination
  relations
    define at_seminary: [seminary]
    define editor: staff from at_seminary

type order
  relations
    define ordination_ref: [ordination]
    define seminary_ref: [seminary]
    define viewer: admin from seminary_ref or staff from seminary_ref
    define editor: admin from seminary_ref or staff from seminary_ref

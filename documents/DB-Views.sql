--
-- File generated with SQLiteStudio v3.4.4 on Tue Nov 19 15:50:40 2024
--
-- Text encoding used: UTF-8
--
PRAGMA foreign_keys = off;
BEGIN TRANSACTION;

-- View: CalculateGPA
CREATE VIEW CalculateGPA AS SELECT 
    s.LastName || ', ' || s.FirstName as FullName,
    (TOTAL(Sem1_Credits) + TOTAL(Sem2_Credits)) as TotalCreditsEarned,
    (TOTAL(Sem1_PotentialCredits) + TOTAL(Sem2_PotentialCredits)) as TotalCreditsPossible,
    (TOTAL(Sem1_GPA * Sem1_PotentialCredits) + TOTAL(Sem2_GPA * Sem2_PotentialCredits)) as WeightedCred,
    ROUND((TOTAL(Sem1_GPA * Sem1_PotentialCredits) + TOTAL(Sem2_GPA * Sem2_PotentialCredits)) / (TOTAL(Sem1_PotentialCredits) + TOTAL(Sem2_PotentialCredits)),2) as CumGPA
FROM HistoricalGrades as hg
INNER JOIN Seminarians as s ON s.ID = hg.SemID
WHERE SemID = (SELECT ID FROM Seminarians WHERE LastName LIKE 'DeLallo' AND FirstName LIKE 'Phillip')
GROUP BY SemID;

-- View: CompleteDocFile
CREATE VIEW CompleteDocFile AS SELECT sem.LastName || ', ' || sem.FirstName as FullName, sem.ID, doc.Skills, doc.MusicalAbility,
    doc.ApplicationEntered, doc.AcceptanceSent, doc.BaptismalCertificate, doc.ConfirmationCertificate, doc.CatholicMarriage, doc.MarriageCertificate,
    doc.AcademicTranscripts, doc.ReferenceLetter, doc.ReferenceLetterFrom, doc.ApplicationNotes, doc.OtherNotes
    
FROM DocumentFile as doc
INNER JOIN Seminarians as sem on sem.ID = doc.SemID
ORDER BY sem.CurrentYear DESC, sem.LastName ASC, sem.FirstName ASC;

-- View: CompleteSeminarianList
CREATE VIEW CompleteSeminarianList AS SELECT ID, LastName, FirstName, yr.Description as Year, st.Description as Status, DOB,
    ((strftime('%Y', 'now') - strftime('%Y', DOB)) - (strftime('%m-%d', 'now') < strftime('%m-%d', DOB))) as Age,
    Phone, Street1, City, State, ZIP, Country, Diocese
FROM Seminarians as s
INNER JOIN YearNames yr ON yr.ID = s.CurrentYear
INNER JOIN StatusCodes st ON st.ID = s.StatusCode
WHERE StatusCode = 1 OR StatusCode = 2
ORDER BY CurrentYear DESC, LOWER(LastName) ASC, LOWER(FirstName) ASC;

-- View: DepartmentsBySeminarians
CREATE VIEW DepartmentsBySeminarians AS SELECT s.LastName, s.FirstName, yr.Description, dep.DeptName, da.DeptID

FROM DepartmentAssignments as da

INNER JOIN Seminarians as s ON s.ID = da.SemID
INNER JOIN Departments as dep ON dep.ID = da.DeptID
INNER JOIN YearNames as yr ON yr.ID = s.CurrentYear

WHERE s.StatusCode = 1

ORDER BY s.CurrentYear DESC, s.LastName ASC, s.FirstName ASC;

-- View: Departures-CurrentYear
CREATE VIEW "Departures-CurrentYear" AS SELECT ROW_NUMBER() OVER(ORDER BY EntryDate) as Num, en.YearDateName, sem.ID, sem.LastName, sem.FirstName, sem.City, sem.State, sem.Country, yr.Description as Departed_From, en.EntryDate, en.Notes
FROM Enrollments as en
INNER JOIN Seminarians as sem ON sem.ID = en.SemID
INNER JOIN YearNames as yr ON yr.ID = en.PreviousYear
WHERE YearDateID = 23 /* Change to desired year */
    AND NewStatusCode = -1 /* Indicates they left the Seminary */
ORDER BY EntryDate ASC;

-- View: Enrollments-WithNames
CREATE VIEW "Enrollments-WithNames" AS SELECT s.ID, s.LastName, s.FirstName, en.YearDateID, en.YearDateName,en.PreviousStatusCode, en.NewStatusCode,
    en.PreviousYear, en.NewYear, en.Notes, en.EntryDate

FROM Enrollments en

INNER JOIN Seminarians s ON (s.ID = en.SemID)

ORDER BY en.EntryDate ASC;

-- View: Enrollments-WithNamesAndDesc
CREATE VIEW "Enrollments-WithNamesAndDesc" AS SELECT s.ID, s.LastName, s.FirstName, en.YearDateID, en.YearDateName,
    (SELECT Description FROM StatusCodes WHERE ID = en.PreviousStatusCode) PreviousStatus,
    (SELECT Description FROM StatusCodes WHERE ID = en.NewStatusCode) NewStatus,
    (SELECT Description FROM YearNames WHERE ID = en.PreviousYear) PreviousYear,
    (SELECT Description FROM YearNames WHERE ID = en.NewYear) NewYear,
    en.Notes, en.EntryDate, en.ID

FROM Enrollments en

INNER JOIN Seminarians s ON (s.ID = en.SemID)

ORDER BY en.EntryDate ASC;

-- View: Export-ForRegister
CREATE VIEW "Export-ForRegister" AS SELECT ordName.LatinName, sem.LastName, sem.FirstName, sem.MiddleName, sem.DOB, sem.Diocese, sem.Street1, sem.Street2, sem.City, sem.State, sem.ZIP, sem.Country
FROM Ordinations as ordin
    INNER JOIN Orders as ord ON ord.OrdinationsID = ordin.ID
    INNER JOIN Seminarians as sem ON sem.ID = ord.SemID
    INNER JOIN OrderNames as ordName ON ordName.ID = ord.OrderID
WHERE ordin.ID = 16
ORDER BY ordName.ID desc, sem.LastName asc;

-- View: Export-Ordinations
CREATE VIEW "Export-Ordinations" AS SELECT sem.LastName, sem.FirstName, sem.City, sem.State, sem.Country, sem.Diocese, ordName.LatinName,
    CASE WHEN sem.Country LIKE 'USA' THEN sem.City || ', ' || sem.State 
            WHEN sem.Country LIKE 'Canada' THEN sem.State || ', ' || sem.Country
            ELSE sem.City || ', ' || sem.Country END as Home
FROM Ordinations as ordin
INNER JOIN Orders as ord ON ord.OrdinationsID = ordin.ID
INNER JOIN Seminarians as sem ON sem.ID = ord.SemID
INNER JOIN OrderNames as ordName ON ordName.ID = ord.OrderID
WHERE ordin.ID IN (18,19)
ORDER BY ordin.ID DESC, ord.OrderID DESC, sem.LastName ASC, sem.FirstName ASC;

-- View: FormattedGrades
CREATE VIEW FormattedGrades AS SELECT sem.LastName || ', ' || sem.FirstName AS FullName,
       sem.CurrentYear AS YearNum,
       yr.Description AS Class,
       sub1.CourseName AS Course1,
       sub1.Professor AS Prof1,
       sub1.Sem1_Percent AS Grade1_Sem1,
       sub1.Sem1_Credits AS Credit1_Sem1,
       sub1.Sem2_Percent AS Grade1_Sem2,
       sub1.Sem2_Credits AS Credit1_Sem2,
       CASE WHEN sub1.Sem2_Percent IS NULL THEN sub1.Sem1_Percent WHEN sub1.Sem1_Percent IS NULL THEN sub1.Sem2_Percent ELSE round( (sub1.Sem1_Percent + sub1.Sem2_Percent) / 2, 0) END AS Avg_Course1,
       CASE WHEN (sub1.Sem1_Credits IS NULL AND 
                  sub1.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub1.Sem1_Credits, 0) + ifnull(sub1.Sem2_Credits, 0) END AS TotCred_Course1,
       sub2.CourseName AS Course2,
       sub2.Professor AS Prof2,
       sub2.Sem1_Percent AS Grade2_Sem1,
       sub2.Sem1_Credits AS Credit2_Sem1,
       sub2.Sem2_Percent AS Grade2_Sem2,
       sub2.Sem2_Credits AS Credit2_Sem2,
       CASE WHEN sub2.Sem2_Percent IS NULL THEN sub2.Sem1_Percent WHEN sub2.Sem1_Percent IS NULL THEN sub2.Sem2_Percent ELSE round( (sub2.Sem1_Percent + sub2.Sem2_Percent) / 2, 0) END AS Avg_Course2,
       CASE WHEN (sub2.Sem1_Credits IS NULL AND 
                  sub2.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub2.Sem1_Credits, 0) + ifnull(sub2.Sem2_Credits, 0) END AS TotCred_Course2,
       sub3.CourseName AS Course3,
       sub3.Professor AS Prof3,
       sub3.Sem1_Percent AS Grade3_Sem1,
       sub3.Sem1_Credits AS Credit3_Sem1,
       sub3.Sem2_Percent AS Grade3_Sem2,
       sub3.Sem2_Credits AS Credit3_Sem2,
       CASE WHEN sub3.Sem2_Percent IS NULL THEN sub3.Sem1_Percent WHEN sub3.Sem1_Percent IS NULL THEN sub3.Sem2_Percent ELSE round( (sub3.Sem1_Percent + sub3.Sem2_Percent) / 2, 0) END AS Avg_Course3,
       CASE WHEN (sub3.Sem1_Credits IS NULL AND 
                  sub3.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub3.Sem1_Credits, 0) + ifnull(sub3.Sem2_Credits, 0) END AS TotCred_Course3,
       sub4.CourseName AS Course4,
       sub4.Professor AS Prof4,
       sub4.Sem1_Percent AS Grade4_Sem1,
       sub4.Sem1_Credits AS Credit4_Sem1,
       sub4.Sem2_Percent AS Grade4_Sem2,
       sub4.Sem2_Credits AS Credit4_Sem2,
       CASE WHEN sub4.Sem2_Percent IS NULL THEN sub4.Sem1_Percent WHEN sub4.Sem1_Percent IS NULL THEN sub4.Sem2_Percent ELSE round( (sub4.Sem1_Percent + sub4.Sem2_Percent) / 2, 0) END AS Avg_Course4,
       CASE WHEN (sub4.Sem1_Credits IS NULL AND 
                  sub4.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub4.Sem1_Credits, 0) + ifnull(sub4.Sem2_Credits, 0) END AS TotCred_Course4,
       sub5.CourseName AS Course5,
       sub5.Professor AS Prof5,
       sub5.Sem1_Percent AS Grade5_Sem1,
       sub5.Sem1_Credits AS Credit5_Sem1,
       sub5.Sem2_Percent AS Grade5_Sem2,
       sub5.Sem2_Credits AS Credit5_Sem2,
       CASE WHEN sub5.Sem2_Percent IS NULL THEN sub5.Sem1_Percent WHEN sub5.Sem1_Percent IS NULL THEN sub5.Sem2_Percent ELSE round( (sub5.Sem1_Percent + sub5.Sem2_Percent) / 2, 0) END AS Avg_Course5,
       CASE WHEN (sub5.Sem1_Credits IS NULL AND 
                  sub5.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub5.Sem1_Credits, 0) + ifnull(sub5.Sem2_Credits, 0) END AS TotCred_Course5,
       sub6.CourseName AS Course6,
       sub6.Professor AS Prof6,
       sub6.Sem1_Percent AS Grade6_Sem1,
       sub6.Sem1_Credits AS Credit6_Sem1,
       sub6.Sem2_Percent AS Grade6_Sem2,
       sub6.Sem2_Credits AS Credit6_Sem2,
       CASE WHEN sub6.Sem2_Percent IS NULL THEN sub6.Sem1_Percent WHEN sub6.Sem1_Percent IS NULL THEN sub6.Sem2_Percent ELSE round( (sub6.Sem1_Percent + sub6.Sem2_Percent) / 2, 0) END AS Avg_Course6,
       CASE WHEN (sub6.Sem1_Credits IS NULL AND 
                  sub6.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub6.Sem1_Credits, 0) + ifnull(sub6.Sem2_Credits, 0) END AS TotCred_Course6,
       sub7.CourseName AS Course7,
       sub7.Professor AS Prof7,
       sub7.Sem1_Percent AS Grade7_Sem1,
       sub7.Sem1_Credits AS Credit7_Sem1,
       sub7.Sem2_Percent AS Grade7_Sem2,
       sub7.Sem2_Credits AS Credit7_Sem2,
       CASE WHEN sub7.Sem2_Percent IS NULL THEN sub7.Sem1_Percent WHEN sub7.Sem1_Percent IS NULL THEN sub7.Sem2_Percent ELSE round( (sub7.Sem1_Percent + sub7.Sem2_Percent) / 2, 0) END AS Avg_Course7,
       CASE WHEN (sub7.Sem1_Credits IS NULL AND 
                  sub7.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub7.Sem1_Credits, 0) + ifnull(sub7.Sem2_Credits, 0) END AS TotCred_Course7,
       round(avg1.Sem1Avg,0) AS Sem1_Average,
       round(avg2.Sem2Avg,0) AS Sem2_Average,
       CASE WHEN cred2.Sem2Cred IS NULL THEN cred1.Sem1Cred ELSE (cred1.Sem1Cred + cred2.Sem2Cred) END AS Total_Credits
  FROM Seminarians AS sem
       LEFT OUTER JOIN
       YearNames yr ON yr.ID = sem.CurrentYear
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 1
       )
       AS sub1 ON sub1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 2
       )
       AS sub2 ON sub2.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 3
       )
       AS sub3 ON sub3.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 4
       )
       AS sub4 ON sub4.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 5
       )
       AS sub5 ON sub5.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 6
       )
       AS sub6 ON sub6.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 7
       )
       AS sub7 ON sub7.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, avg(Sem1_Percent) as Sem1Avg
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS avg1 ON avg1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, avg(Sem2_Percent) as Sem2Avg
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS avg2 ON avg2.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, sum(Sem1_Credits) as Sem1Cred
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS cred1 ON cred1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, sum(Sem2_Credits) as Sem2Cred
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS cred2 ON cred2.SemID = sem.ID
 WHERE sem.ID IN (SELECT SemID FROM CurrentGrades GROUP BY SemID)
 ORDER BY sem.CurrentYear DESC, FullName ASC;

-- View: FormattedGrades-FailingOnly-1stSem
CREATE VIEW "FormattedGrades-FailingOnly-1stSem" AS SELECT sem.LastName || ', ' || sem.FirstName AS FullName,
       sem.CurrentYear AS YearNum,
       yr.Description AS Class,
       sub1.CourseName AS Course1,
       sub1.Professor AS Prof1,
       sub1.Sem1_Percent AS Grade1_Sem1,
       sub1.Sem1_Credits AS Credit1_Sem1,
       sub1.Sem2_Percent AS Grade1_Sem2,
       sub1.Sem2_Credits AS Credit1_Sem2,
       CASE WHEN sub1.Sem2_Percent IS NULL THEN sub1.Sem1_Percent WHEN sub1.Sem1_Percent IS NULL THEN sub1.Sem2_Percent ELSE round( (sub1.Sem1_Percent + sub1.Sem2_Percent) / 2, 0) END AS Avg_Course1,
       CASE WHEN (sub1.Sem1_Credits IS NULL AND 
                  sub1.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub1.Sem1_Credits, 0) + ifnull(sub1.Sem2_Credits, 0) END AS TotCred_Course1,
       sub2.CourseName AS Course2,
       sub2.Professor AS Prof2,
       sub2.Sem1_Percent AS Grade2_Sem1,
       sub2.Sem1_Credits AS Credit2_Sem1,
       sub2.Sem2_Percent AS Grade2_Sem2,
       sub2.Sem2_Credits AS Credit2_Sem2,
       CASE WHEN sub2.Sem2_Percent IS NULL THEN sub2.Sem1_Percent WHEN sub2.Sem1_Percent IS NULL THEN sub2.Sem2_Percent ELSE round( (sub2.Sem1_Percent + sub2.Sem2_Percent) / 2, 0) END AS Avg_Course2,
       CASE WHEN (sub2.Sem1_Credits IS NULL AND 
                  sub2.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub2.Sem1_Credits, 0) + ifnull(sub2.Sem2_Credits, 0) END AS TotCred_Course2,
       sub3.CourseName AS Course3,
       sub3.Professor AS Prof3,
       sub3.Sem1_Percent AS Grade3_Sem1,
       sub3.Sem1_Credits AS Credit3_Sem1,
       sub3.Sem2_Percent AS Grade3_Sem2,
       sub3.Sem2_Credits AS Credit3_Sem2,
       CASE WHEN sub3.Sem2_Percent IS NULL THEN sub3.Sem1_Percent WHEN sub3.Sem1_Percent IS NULL THEN sub3.Sem2_Percent ELSE round( (sub3.Sem1_Percent + sub3.Sem2_Percent) / 2, 0) END AS Avg_Course3,
       CASE WHEN (sub3.Sem1_Credits IS NULL AND 
                  sub3.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub3.Sem1_Credits, 0) + ifnull(sub3.Sem2_Credits, 0) END AS TotCred_Course3,
       sub4.CourseName AS Course4,
       sub4.Professor AS Prof4,
       sub4.Sem1_Percent AS Grade4_Sem1,
       sub4.Sem1_Credits AS Credit4_Sem1,
       sub4.Sem2_Percent AS Grade4_Sem2,
       sub4.Sem2_Credits AS Credit4_Sem2,
       CASE WHEN sub4.Sem2_Percent IS NULL THEN sub4.Sem1_Percent WHEN sub4.Sem1_Percent IS NULL THEN sub4.Sem2_Percent ELSE round( (sub4.Sem1_Percent + sub4.Sem2_Percent) / 2, 0) END AS Avg_Course4,
       CASE WHEN (sub4.Sem1_Credits IS NULL AND 
                  sub4.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub4.Sem1_Credits, 0) + ifnull(sub4.Sem2_Credits, 0) END AS TotCred_Course4,
       sub5.CourseName AS Course5,
       sub5.Professor AS Prof5,
       sub5.Sem1_Percent AS Grade5_Sem1,
       sub5.Sem1_Credits AS Credit5_Sem1,
       sub5.Sem2_Percent AS Grade5_Sem2,
       sub5.Sem2_Credits AS Credit5_Sem2,
       CASE WHEN sub5.Sem2_Percent IS NULL THEN sub5.Sem1_Percent WHEN sub5.Sem1_Percent IS NULL THEN sub5.Sem2_Percent ELSE round( (sub5.Sem1_Percent + sub5.Sem2_Percent) / 2, 0) END AS Avg_Course5,
       CASE WHEN (sub5.Sem1_Credits IS NULL AND 
                  sub5.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub5.Sem1_Credits, 0) + ifnull(sub5.Sem2_Credits, 0) END AS TotCred_Course5,
       sub6.CourseName AS Course6,
       sub6.Professor AS Prof6,
       sub6.Sem1_Percent AS Grade6_Sem1,
       sub6.Sem1_Credits AS Credit6_Sem1,
       sub6.Sem2_Percent AS Grade6_Sem2,
       sub6.Sem2_Credits AS Credit6_Sem2,
       CASE WHEN sub6.Sem2_Percent IS NULL THEN sub6.Sem1_Percent WHEN sub6.Sem1_Percent IS NULL THEN sub6.Sem2_Percent ELSE round( (sub6.Sem1_Percent + sub6.Sem2_Percent) / 2, 0) END AS Avg_Course6,
       CASE WHEN (sub6.Sem1_Credits IS NULL AND 
                  sub6.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub6.Sem1_Credits, 0) + ifnull(sub6.Sem2_Credits, 0) END AS TotCred_Course6,
       sub7.CourseName AS Course7,
       sub7.Professor AS Prof7,
       sub7.Sem1_Percent AS Grade7_Sem1,
       sub7.Sem1_Credits AS Credit7_Sem1,
       sub7.Sem2_Percent AS Grade7_Sem2,
       sub7.Sem2_Credits AS Credit7_Sem2,
       CASE WHEN sub7.Sem2_Percent IS NULL THEN sub7.Sem1_Percent WHEN sub7.Sem1_Percent IS NULL THEN sub7.Sem2_Percent ELSE round( (sub7.Sem1_Percent + sub7.Sem2_Percent) / 2, 0) END AS Avg_Course7,
       CASE WHEN (sub7.Sem1_Credits IS NULL AND 
                  sub7.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub7.Sem1_Credits, 0) + ifnull(sub7.Sem2_Credits, 0) END AS TotCred_Course7,
       round(avg1.Sem1Avg,0) AS Sem1_Average,
       round(avg2.Sem2Avg,0) AS Sem2_Average,
       CASE WHEN cred2.Sem2Cred IS NULL THEN cred1.Sem1Cred ELSE (cred1.Sem1Cred + cred2.Sem2Cred) END AS Total_Credits
  FROM Seminarians AS sem
       LEFT OUTER JOIN
       YearNames yr ON yr.ID = sem.CurrentYear
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 1
       )
       AS sub1 ON sub1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 2
       )
       AS sub2 ON sub2.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 3
       )
       AS sub3 ON sub3.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 4
       )
       AS sub4 ON sub4.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 5
       )
       AS sub5 ON sub5.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 6
       )
       AS sub6 ON sub6.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 7
       )
       AS sub7 ON sub7.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, avg(Sem1_Percent) as Sem1Avg
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS avg1 ON avg1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, avg(Sem2_Percent) as Sem2Avg
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS avg2 ON avg2.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, sum(Sem1_Credits) as Sem1Cred
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS cred1 ON cred1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, sum(Sem2_Credits) as Sem2Cred
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS cred2 ON cred2.SemID = sem.ID
 WHERE sem.ID IN (SELECT SemID FROM CurrentGrades GROUP BY SemID) AND 
       (sub1.Sem1_Grade LIKE 'F' OR 
        sub2.Sem1_Grade LIKE 'F' OR 
        sub3.Sem1_Grade LIKE 'F' OR 
        sub4.Sem1_Grade LIKE 'F' OR 
        sub5.Sem1_Grade LIKE 'F' OR 
        sub6.Sem1_Grade LIKE 'F' OR 
        sub7.Sem1_Grade LIKE 'F')
ORDER BY sem.CurrentYear DESC, FullName ASC;

-- View: FormattedGrades-FailingOnly-2ndSem
CREATE VIEW "FormattedGrades-FailingOnly-2ndSem" AS SELECT sem.LastName || ', ' || sem.FirstName AS FullName,
       sem.CurrentYear AS YearNum,
       yr.Description AS Class,
       sub1.CourseName AS Course1,
       sub1.Professor AS Prof1,
       sub1.Sem1_Percent AS Grade1_Sem1,
       sub1.Sem1_Credits AS Credit1_Sem1,
       sub1.Sem2_Percent AS Grade1_Sem2,
       sub1.Sem2_Credits AS Credit1_Sem2,
       CASE WHEN sub1.Sem2_Percent IS NULL THEN sub1.Sem1_Percent WHEN sub1.Sem1_Percent IS NULL THEN sub1.Sem2_Percent ELSE round( (sub1.Sem1_Percent + sub1.Sem2_Percent) / 2, 0) END AS Avg_Course1,
       CASE WHEN (sub1.Sem1_Credits IS NULL AND 
                  sub1.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub1.Sem1_Credits, 0) + ifnull(sub1.Sem2_Credits, 0) END AS TotCred_Course1,
       sub2.CourseName AS Course2,
       sub2.Professor AS Prof2,
       sub2.Sem1_Percent AS Grade2_Sem1,
       sub2.Sem1_Credits AS Credit2_Sem1,
       sub2.Sem2_Percent AS Grade2_Sem2,
       sub2.Sem2_Credits AS Credit2_Sem2,
       CASE WHEN sub2.Sem2_Percent IS NULL THEN sub2.Sem1_Percent WHEN sub2.Sem1_Percent IS NULL THEN sub2.Sem2_Percent ELSE round( (sub2.Sem1_Percent + sub2.Sem2_Percent) / 2, 0) END AS Avg_Course2,
       CASE WHEN (sub2.Sem1_Credits IS NULL AND 
                  sub2.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub2.Sem1_Credits, 0) + ifnull(sub2.Sem2_Credits, 0) END AS TotCred_Course2,
       sub3.CourseName AS Course3,
       sub3.Professor AS Prof3,
       sub3.Sem1_Percent AS Grade3_Sem1,
       sub3.Sem1_Credits AS Credit3_Sem1,
       sub3.Sem2_Percent AS Grade3_Sem2,
       sub3.Sem2_Credits AS Credit3_Sem2,
       CASE WHEN sub3.Sem2_Percent IS NULL THEN sub3.Sem1_Percent WHEN sub3.Sem1_Percent IS NULL THEN sub3.Sem2_Percent ELSE round( (sub3.Sem1_Percent + sub3.Sem2_Percent) / 2, 0) END AS Avg_Course3,
       CASE WHEN (sub3.Sem1_Credits IS NULL AND 
                  sub3.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub3.Sem1_Credits, 0) + ifnull(sub3.Sem2_Credits, 0) END AS TotCred_Course3,
       sub4.CourseName AS Course4,
       sub4.Professor AS Prof4,
       sub4.Sem1_Percent AS Grade4_Sem1,
       sub4.Sem1_Credits AS Credit4_Sem1,
       sub4.Sem2_Percent AS Grade4_Sem2,
       sub4.Sem2_Credits AS Credit4_Sem2,
       CASE WHEN sub4.Sem2_Percent IS NULL THEN sub4.Sem1_Percent WHEN sub4.Sem1_Percent IS NULL THEN sub4.Sem2_Percent ELSE round( (sub4.Sem1_Percent + sub4.Sem2_Percent) / 2, 0) END AS Avg_Course4,
       CASE WHEN (sub4.Sem1_Credits IS NULL AND 
                  sub4.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub4.Sem1_Credits, 0) + ifnull(sub4.Sem2_Credits, 0) END AS TotCred_Course4,
       sub5.CourseName AS Course5,
       sub5.Professor AS Prof5,
       sub5.Sem1_Percent AS Grade5_Sem1,
       sub5.Sem1_Credits AS Credit5_Sem1,
       sub5.Sem2_Percent AS Grade5_Sem2,
       sub5.Sem2_Credits AS Credit5_Sem2,
       CASE WHEN sub5.Sem2_Percent IS NULL THEN sub5.Sem1_Percent WHEN sub5.Sem1_Percent IS NULL THEN sub5.Sem2_Percent ELSE round( (sub5.Sem1_Percent + sub5.Sem2_Percent) / 2, 0) END AS Avg_Course5,
       CASE WHEN (sub5.Sem1_Credits IS NULL AND 
                  sub5.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub5.Sem1_Credits, 0) + ifnull(sub5.Sem2_Credits, 0) END AS TotCred_Course5,
       sub6.CourseName AS Course6,
       sub6.Professor AS Prof6,
       sub6.Sem1_Percent AS Grade6_Sem1,
       sub6.Sem1_Credits AS Credit6_Sem1,
       sub6.Sem2_Percent AS Grade6_Sem2,
       sub6.Sem2_Credits AS Credit6_Sem2,
       CASE WHEN sub6.Sem2_Percent IS NULL THEN sub6.Sem1_Percent WHEN sub6.Sem1_Percent IS NULL THEN sub6.Sem2_Percent ELSE round( (sub6.Sem1_Percent + sub6.Sem2_Percent) / 2, 0) END AS Avg_Course6,
       CASE WHEN (sub6.Sem1_Credits IS NULL AND 
                  sub6.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub6.Sem1_Credits, 0) + ifnull(sub6.Sem2_Credits, 0) END AS TotCred_Course6,
       sub7.CourseName AS Course7,
       sub7.Professor AS Prof7,
       sub7.Sem1_Percent AS Grade7_Sem1,
       sub7.Sem1_Credits AS Credit7_Sem1,
       sub7.Sem2_Percent AS Grade7_Sem2,
       sub7.Sem2_Credits AS Credit7_Sem2,
       CASE WHEN sub7.Sem2_Percent IS NULL THEN sub7.Sem1_Percent WHEN sub7.Sem1_Percent IS NULL THEN sub7.Sem2_Percent ELSE round( (sub7.Sem1_Percent + sub7.Sem2_Percent) / 2, 0) END AS Avg_Course7,
       CASE WHEN (sub7.Sem1_Credits IS NULL AND 
                  sub7.Sem2_Credits IS NULL) THEN NULL ELSE ifnull(sub7.Sem1_Credits, 0) + ifnull(sub7.Sem2_Credits, 0) END AS TotCred_Course7,
       round(avg1.Sem1Avg,0) AS Sem1_Average,
       round(avg2.Sem2Avg,0) AS Sem2_Average,
       CASE WHEN cred2.Sem2Cred IS NULL THEN cred1.Sem1Cred ELSE (cred1.Sem1Cred + cred2.Sem2Cred) END AS Total_Credits
  FROM Seminarians AS sem
       INNER JOIN
       YearNames yr ON yr.ID = sem.CurrentYear
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 1
       )
       AS sub1 ON sub1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 2
       )
       AS sub2 ON sub2.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 3
       )
       AS sub3 ON sub3.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 4
       )
       AS sub4 ON sub4.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 5
       )
       AS sub5 ON sub5.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 6
       )
       AS sub6 ON sub6.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID,
                  CourseName,
                  CourseOrder,
                  Professor,
                  Sem1_Grade,
                  Sem1_Percent,
                  Sem1_Credits,
                  Sem2_Grade,
                  Sem2_Percent,
                  Sem2_Credits
             FROM CurrentGrades
            WHERE CourseOrder = 7
       )
       AS sub7 ON sub7.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, avg(Sem1_Percent) as Sem1Avg
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS avg1 ON avg1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, avg(Sem2_Percent) as Sem2Avg
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS avg2 ON avg2.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, sum(Sem1_Credits) as Sem1Cred
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS cred1 ON cred1.SemID = sem.ID
       LEFT OUTER JOIN
       (
           SELECT SemID, sum(Sem2_Credits) as Sem2Cred
           FROM CurrentGrades
           GROUP BY SemID
       )
       AS cred2 ON cred2.SemID = sem.ID
 WHERE sem.StatusCode = 1 AND 
       (sub1.Sem2_Grade LIKE 'F' OR 
        sub2.Sem2_Grade LIKE 'F' OR 
        sub3.Sem2_Grade LIKE 'F' OR 
        sub4.Sem2_Grade LIKE 'F' OR 
        sub5.Sem2_Grade LIKE 'F' OR 
        sub6.Sem2_Grade LIKE 'F' OR 
        sub7.Sem2_Grade LIKE 'F')
 ORDER BY sem.CurrentYear DESC, FullName ASC;

-- View: HistoricalGradeLayout-OneSeminarian
CREATE VIEW "HistoricalGradeLayout-OneSeminarian" AS SELECT Year, SemYearID, CourseName, Sem1_Grade, Sem2_Grade, (IFNULL(Sem1_Credits,0) + IFNULL(Sem2_Credits,0)) TotalCred,
        (IFNULL(Sem1_PotentialCredits,0) + IFNULL(Sem2_PotentialCredits,0)) PotentCred,
        (IFNULL(Sem1_GPA,0) * IFNULL(Sem1_PotentialCredits,0))+(IFNULL(Sem2_GPA,0) * IFNULL(Sem2_PotentialCredits,0)) WeightedPoints
FROM HistoricalGrades
WHERE SemID = (SELECT ID FROM Seminarians WHERE LastName LIKE 'DeLallo' AND FirstName LIKE 'Phillip')
ORDER BY Year ASC, CourseOrder ASC;

-- View: Menzingen-HumanitiesList
CREATE VIEW "Menzingen-HumanitiesList" AS SELECT LastName, FirstName, yr.Description as Year, st.Description as Status, DOB,
    ((strftime('%Y', 'now') - strftime('%Y', DOB)) - (strftime('%m-%d', 'now') < strftime('%m-%d', DOB))) as Age,
    State, Country
FROM Seminarians as s
INNER JOIN YearNames yr ON yr.ID = s.CurrentYear
INNER JOIN StatusCodes st ON st.ID = s.StatusCode
WHERE StatusCode = 1 AND CurrentYear = 0
ORDER BY StatusCode ASC, CurrentYear DESC, LOWER(LastName) ASC, LOWER(FirstName) ASC;

-- View: Menzingen-SpiritualityList
CREATE VIEW "Menzingen-SpiritualityList" AS SELECT LastName, FirstName, yr.Description as Year, BirthCity, BirthState, BirthCountry
FROM Seminarians as s
INNER JOIN YearNames yr ON yr.ID = s.CurrentYear
WHERE StatusCode = 1 AND CurrentYear = 1
ORDER BY LOWER(LastName) ASC, LOWER(FirstName) ASC;

-- View: Menzingen-StartOfYearList
CREATE VIEW "Menzingen-StartOfYearList" AS SELECT LastName, FirstName, yr.Description as Year, st.Description as Status, DOB,
    ((strftime('%Y', 'now') - strftime('%Y', DOB)) - (strftime('%m-%d', 'now') < strftime('%m-%d', DOB))) as Age,
    Diocese, Country,
    (SELECT Notes FROM Enrollments WHERE SemID = s.ID ORDER BY EntryDate DESC LIMIT 1) as LastAction
FROM Seminarians as s
INNER JOIN YearNames yr ON yr.ID = s.CurrentYear
INNER JOIN StatusCodes st ON st.ID = s.StatusCode
WHERE StatusCode = 1 OR StatusCode = 2
ORDER BY StatusCode ASC, CurrentYear DESC, LOWER(LastName) ASC, LOWER(FirstName) ASC;

-- View: OrdinationSummary
CREATE VIEW OrdinationSummary AS SELECT sem.ID,
       sem.LastName || ', ' || sem.FirstName AS FullName,
       cer.Date,
       cer.Bishop,
       ord.OrderID,
       oname.Name,
       ord.Titulum
  FROM Ordinations AS cer
       INNER JOIN
       Orders AS ord ON ord.OrdinationsID = cer.ID
       INNER JOIN
       Seminarians AS sem ON sem.ID = ord.SemID
       INNER JOIN
       OrderNames AS oname ON oname.ID = ord.OrderID
 ORDER BY cer.Date ASC,
          ord.OrderID DESC,
          FullName ASC;

-- View: Professors-StartOfYearList
CREATE VIEW "Professors-StartOfYearList" AS SELECT LastName, FirstName, yr.Description as Year, st.Description as Status,
    (SELECT Notes FROM Enrollments WHERE SemID = s.ID ORDER BY EntryDate DESC LIMIT 1) as Notes,
    DOB, ((strftime('%Y', 'now') - strftime('%Y', DOB)) - (strftime('%m-%d', 'now') < strftime('%m-%d', DOB))) as Age,
    Phone, Email, State, Country
FROM Seminarians as s
INNER JOIN YearNames yr ON yr.ID = s.CurrentYear
INNER JOIN StatusCodes st ON st.ID = s.StatusCode
WHERE StatusCode = 1 OR StatusCode = 2
ORDER BY StatusCode ASC, CurrentYear DESC, LOWER(LastName) ASC, LOWER(FirstName) ASC;

-- View: RecordingAcceptance
CREATE VIEW RecordingAcceptance AS SELECT d.SemID, s.LastName, s.FirstName, d.ApplicationEntered, d.AcceptanceSent
FROM DocumentFile as d
INNER JOIN Seminarians as s ON s.ID = d.SemID
WHERE strftime('%Y', d.ApplicationEntered) LIKE '2021' AND s.StatusCode <> -2
ORDER BY LastName ASC, FirstName ASC;

-- View: SeminariansPerYear
CREATE VIEW SeminariansPerYear AS SELECT s.CurrentYear OrderNum, yr.Description Year, COUNT(s.ID) Number
FROM Seminarians as s
INNER JOIN YearNames as yr ON yr.ID = s.CurrentYear
WHERE s.StatusCode = 1
GROUP BY s.CurrentYear

UNION

SELECT 8, 'Total Theologians', COUNT(s.ID) Number
FROM Seminarians as s
WHERE s.StatusCode = 1 AND s.CurrentYear IN (4,5,6)
GROUP BY s.StatusCode

UNION

SELECT 7, 'Total Philosophers', COUNT(s.ID) Number
FROM Seminarians as s
WHERE s.StatusCode = 1 AND s.CurrentYear IN (2,3)
GROUP BY s.StatusCode

ORDER BY OrderNum DESC;

-- View: SimpleCurrentGrades
CREATE VIEW SimpleCurrentGrades AS SELECT gr.ID,
       gr.SemID,
       sem.LastName || ', ' || sem.FirstName AS FullName,
       gr.CourseName,
       gr.CourseOrder,
       gr.Professor,
       gr.Sem1_Credits,
       gr.Sem1_Percent,
       gr.Sem1_Grade,
       gr.Sem2_Credits,
       gr.Sem2_Percent,
       gr.Sem2_Grade
  FROM CurrentGrades AS gr
       INNER JOIN
       Seminarians AS sem ON sem.ID = gr.SemID
 WHERE sem.StatusCode = 1
 ORDER BY FullName ASC,
          gr.CourseOrder ASC;

-- View: SimpleSeminarianList
CREATE VIEW SimpleSeminarianList AS SELECT LastName || ', ' || FirstName as FullName, yr.Description as Year, sc.Description as Status, RoomNum, City, State, Country, Diocese
FROM Seminarians as s
INNER JOIN YearNames yr ON yr.ID = s.CurrentYear
INNER JOIN StatusCodes sc ON sc.ID = s.StatusCode
WHERE StatusCode = 1 OR StatusCode = 2 OR StatusCode = 3
ORDER BY s.CurrentYear desc, s.LastName asc, s.FirstName asc;

COMMIT TRANSACTION;
PRAGMA foreign_keys = on;

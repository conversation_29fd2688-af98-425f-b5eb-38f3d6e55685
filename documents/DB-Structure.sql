--
-- File generated with SQLiteStudio v3.4.4 on Tue Oct 1 10:23:26 2024
--
-- Text encoding used: UTF-8
--
PRAGMA foreign_keys = off;
BEGIN TRANSACTION;

-- Table: CurrentGrades
CREATE TABLE CurrentGrades (ID INTEGER PRIMARY KEY ASC ON CONFLICT REPLACE AUTOINCREMENT NOT NULL UNIQUE, SemID INT REFERENCES Seminarians (ID) NOT NULL, SemYearID INT REFERENCES YearNames (ID), CourseName STRING, CourseOrder INT, Professor STRING, Sem1_Credits INT, Sem1_PotentialCredits INT, Sem1_Percent INT, Sem1_Grade STRING, Sem1_GPA DECIMAL, Sem2_Credits INT, Sem2_PotentialCredits INT, Sem2_Percent INT, Sem2_Grade STRING, Sem2_GPA DECIMAL);

-- Table: DocumentFile
CREATE TABLE DocumentFile (ID INTEGER PRIMARY KEY, SemID INT REFERENCES Seminarians (ID) ON DELETE SET NULL, ApplicationEntered DATE, AcceptanceSent DATE, BaptismalCertificate BOOLEAN, ConfirmationCertificate BOOLEAN, CatholicMarriage BOOLEAN, MarriageCertificate BOOLEAN, AcademicTranscripts BOOLEAN, ReferenceLetter BOOLEAN, ReferenceLetterFrom STRING, ApplicationNotes STRING, SSPXSchool STRING, PerpetualEngagement DATE, Skills STRING, MusicalAbility STRING, OtherNotes STRING);

-- Table: Enrollments
CREATE TABLE Enrollments (ID INTEGER PRIMARY KEY AUTOINCREMENT UNIQUE NOT NULL, SemID INT REFERENCES Seminarians (ID) NOT NULL, YearDateID INT, YearDateName STRING, PreviousStatusCode INT REFERENCES StatusCodes (ID), NewStatusCode INT REFERENCES StatusCodes (ID), PreviousYear INT REFERENCES YearNames (ID), NewYear INT REFERENCES YearNames (ID), Notes VARCHAR, EntryDate DATE NOT NULL);

-- Table: HistoricalGrades
CREATE TABLE HistoricalGrades (ID INTEGER PRIMARY KEY ASC ON CONFLICT REPLACE AUTOINCREMENT NOT NULL UNIQUE, SemID INT REFERENCES Seminarians (ID) NOT NULL, Year STRING, SemYearID INT REFERENCES YearNames (ID), CourseName STRING, CourseOrder INT, Professor STRING, Sem1_Credits INT, Sem1_PotentialCredits INT, Sem1_Percent INT, Sem1_Grade STRING, Sem1_GPA DECIMAL, Sem2_Credits INT, Sem2_PotentialCredits INT, Sem2_Percent INT, Sem2_Grade STRING, Sem2_GPA DECIMAL);

-- Table: OrderNames
CREATE TABLE OrderNames (ID INTEGER PRIMARY KEY, Name STRING, LatinName STRING);

-- Table: Orders
CREATE TABLE Orders (ID INTEGER PRIMARY KEY, OrdinationsID INT REFERENCES Ordinations (ID), SemID INT REFERENCES Seminarians (ID), OrderID INT REFERENCES OrderNames (ID), Titulum STRING);

-- Table: Ordinations
CREATE TABLE Ordinations (ID INTEGER PRIMARY KEY, Date DATE, Bishop STRING, PlaceName STRING, Place STRING, Notes STRING);

-- Table: Seminarians
CREATE TABLE Seminarians (ID INTEGER PRIMARY KEY AUTOINCREMENT, LastName STRING NOT NULL, FirstName STRING, MiddleName STRING, Street1 STRING, Street2 STRING, City STRING, State STRING, ZIP STRING, Country STRING, MailingStreet STRING, Phone STRING, Email STRING, Citizenship STRING, Diocese STRING, DOB DATE, BirthCity STRING, BirthState STRING, BirthCountry STRING, StatusCode INT NOT NULL DEFAULT (1) REFERENCES StatusCodes (ID), CurrentYear INT REFERENCES YearNames (ID), RoomNum INT, Notes VARCHAR);

-- Table: StatusCodes
CREATE TABLE StatusCodes (ID INTEGER PRIMARY KEY UNIQUE NOT NULL, Description STRING NOT NULL);

-- Table: YearCodes
CREATE TABLE YearCodes (Code INT PRIMARY KEY, Name STRING);

-- Table: YearNames
CREATE TABLE YearNames (ID INTEGER PRIMARY KEY UNIQUE NOT NULL, Description STRING NOT NULL);

-- Trigger: Application_Accepted
CREATE TRIGGER Application_Accepted AFTER UPDATE OF AcceptanceSent ON DocumentFile WHEN OLD.AcceptanceSent IS NULL AND NEW.AcceptanceSent IS NOT NULL BEGIN INSERT INTO Enrollments (SemID, YearDateID, YearDateName, PreviousStatusCode, NewStatusCode, PreviousYear, NewYear, Notes, EntryDate) VALUES (NEW.SemID, substr(strftime('%Y', NEW.AcceptanceSent), 3), (SELECT Name FROM YearCodes WHERE Code = substr(strftime('%Y', NEW.AcceptanceSent), 3)), 0, 1, NULL, 0, 'Application Accepted', date('now')); END;

-- Trigger: Update_Status
CREATE TRIGGER Update_Status AFTER INSERT ON Enrollments WHEN NEW.NewStatusCode = (SELECT NewStatusCode
                FROM Enrollments
                WHERE SemID = NEW.SemID
                ORDER BY Enrollments.EntryDate DESC
                LIMIT 1)   BEGIN UPDATE Seminarians 
    SET StatusCode = NEW.NewStatusCode
    WHERE Seminarians.ID = NEW.SemID
    ; END;

-- Trigger: Update_Year
CREATE TRIGGER Update_Year AFTER INSERT ON Enrollments BEGIN UPDATE Seminarians 
    SET CurrentYear = NEW.NewYear
    WHERE Seminarians.ID = NEW.SemID; END;

COMMIT TRANSACTION;
PRAGMA foreign_keys = on;

ALTER TABLE `enrollments` RENAME TO `course_enrollments`;--> statement-breakpoint
CREATE TABLE `user_activity` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`relationship` text NOT NULL,
	`old` text,
	`new` text,
	`action` text NOT NULL,
	`resource_type` text,
	`resource_id` text,
	`note` text,
	`timestamp` integer NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
DROP TABLE `grades`;--> statement-breakpoint
DROP TABLE `room_assignments`;--> statement-breakpoint
DROP TABLE `status_updates`;--> statement-breakpoint
DROP TABLE `user_roles`;--> statement-breakpoint
PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_course_enrollments` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`seminarian_id` integer NOT NULL,
	`course_offering_id` integer NOT NULL,
	`credits_earned` integer,
	`percent_grade` integer,
	`notes` text,
	FOREIGN KEY (`seminarian_id`) REFERENCES `seminarians`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`course_offering_id`) REFERENCES `course_offerings`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_course_enrollments`("id", "seminarian_id", "course_offering_id", "credits_earned", "percent_grade", "notes") SELECT "id", "seminarian_id", "course_offering_id", "credits_earned", "percent_grade", "notes" FROM `course_enrollments`;--> statement-breakpoint
DROP TABLE `course_enrollments`;--> statement-breakpoint
ALTER TABLE `__new_course_enrollments` RENAME TO `course_enrollments`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `course_enrollments_seminarian_id_course_offering_id_unique` ON `course_enrollments` (`seminarian_id`,`course_offering_id`);--> statement-breakpoint
CREATE TABLE `__new_course_offerings` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`course_id` integer NOT NULL,
	`class_room` text,
	`academic_year` integer NOT NULL,
	`semester` text NOT NULL,
	FOREIGN KEY (`course_id`) REFERENCES `courses`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
INSERT INTO `__new_course_offerings`("id", "course_id", "class_room", "academic_year", "semester") SELECT "id", "course_id", "class_room", "academic_year", "semester" FROM `course_offerings`;--> statement-breakpoint
DROP TABLE `course_offerings`;--> statement-breakpoint
ALTER TABLE `__new_course_offerings` RENAME TO `course_offerings`;--> statement-breakpoint
CREATE UNIQUE INDEX `course_offerings_course_id_academic_year_semester_unique` ON `course_offerings` (`course_id`,`academic_year`,`semester`);--> statement-breakpoint
CREATE TABLE `__new_departments` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`category` text,
	`sub_prefect_seminarian_id` integer,
	FOREIGN KEY (`sub_prefect_seminarian_id`) REFERENCES `seminarians`(`id`) ON UPDATE no action ON DELETE set null
);
--> statement-breakpoint
INSERT INTO `__new_departments`("id", "name", "category", "sub_prefect_seminarian_id") SELECT "id", "name", "category", "sub_prefect_seminarian_id" FROM `departments`;--> statement-breakpoint
DROP TABLE `departments`;--> statement-breakpoint
ALTER TABLE `__new_departments` RENAME TO `departments`;--> statement-breakpoint
CREATE TABLE `__new_users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`email` text,
	`phone` text,
	`honorific` text,
	`first_name` text,
	`last_name` text,
	`username` text NOT NULL,
	`password_hash` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_users`("id", "email", "phone", "honorific", "first_name", "last_name", "username", "password_hash", "is_active") SELECT "id", "email", "phone", "honorific", "first_name", "last_name", "username", "password_hash", "is_active" FROM `users`;--> statement-breakpoint
DROP TABLE `users`;--> statement-breakpoint
ALTER TABLE `__new_users` RENAME TO `users`;--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_unique` ON `users` (`username`);--> statement-breakpoint
ALTER TABLE `seminarians` ADD `class_year` integer;--> statement-breakpoint
ALTER TABLE `seminarians` DROP COLUMN `semester`;
{"version": "6", "dialect": "sqlite", "id": "ec703c9c-b15c-4bf7-9064-6d1a9bf2fb6f", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"applications": {"name": "applications", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "seminarian_id": {"name": "seminarian_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "application_entered": {"name": "application_entered", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "acceptance_sent": {"name": "acceptance_sent", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "baptismal_certificate": {"name": "baptismal_certificate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "confirmation_certificate": {"name": "confirmation_certificate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "catholic_marriage": {"name": "catholic_marriage", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "marriage_certificate": {"name": "marriage_certificate", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "academic_transcripts": {"name": "academic_transcripts", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "reference_letter": {"name": "reference_letter", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "reference_letter_from": {"name": "reference_letter_from", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "application_notes": {"name": "application_notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "sspx_school": {"name": "sspx_school", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "perpetual_engagement": {"name": "perpetual_engagement", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "skills": {"name": "skills", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "musical_ability": {"name": "musical_ability", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"applications_seminarian_id_seminarians_id_fk": {"name": "applications_seminarian_id_seminarians_id_fk", "tableFrom": "applications", "tableTo": "seminarians", "columnsFrom": ["seminarian_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "course_offerings": {"name": "course_offerings", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "course_id": {"name": "course_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "academic_year": {"name": "academic_year", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "semester": {"name": "semester", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"course_offerings_course_id_courses_id_fk": {"name": "course_offerings_course_id_courses_id_fk", "tableFrom": "course_offerings", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "course_offerings_user_id_users_id_fk": {"name": "course_offerings_user_id_users_id_fk", "tableFrom": "course_offerings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "courses": {"name": "courses", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "course_name": {"name": "course_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "department_assignments": {"name": "department_assignments", "columns": {"seminarian_id": {"name": "seminarian_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"department_assignments_seminarian_id_seminarians_id_fk": {"name": "department_assignments_seminarian_id_seminarians_id_fk", "tableFrom": "department_assignments", "tableTo": "seminarians", "columnsFrom": ["seminarian_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "department_assignments_department_id_departments_id_fk": {"name": "department_assignments_department_id_departments_id_fk", "tableFrom": "department_assignments", "tableTo": "departments", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"department_assignments_seminarian_id_department_id_pk": {"columns": ["seminarian_id", "department_id"], "name": "department_assignments_seminarian_id_department_id_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "departments": {"name": "departments", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "prefect_id": {"name": "prefect_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"departments_prefect_id_users_id_fk": {"name": "departments_prefect_id_users_id_fk", "tableFrom": "departments", "tableTo": "users", "columnsFrom": ["prefect_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "enrollments": {"name": "enrollments", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "seminarian_id": {"name": "seminarian_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "academic_year": {"name": "academic_year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "seminary_year": {"name": "seminary_year", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "semester": {"name": "semester", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "entry_date": {"name": "entry_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"enrollments_seminarian_id_seminarians_id_fk": {"name": "enrollments_seminarian_id_seminarians_id_fk", "tableFrom": "enrollments", "tableTo": "seminarians", "columnsFrom": ["seminarian_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "grades": {"name": "grades", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "seminarian_id": {"name": "seminarian_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "course_offering_id": {"name": "course_offering_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "credits_earned": {"name": "credits_earned", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "percent_grade": {"name": "percent_grade", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"grades_seminarian_id_seminarians_id_fk": {"name": "grades_seminarian_id_seminarians_id_fk", "tableFrom": "grades", "tableTo": "seminarians", "columnsFrom": ["seminarian_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "grades_course_offering_id_course_offerings_id_fk": {"name": "grades_course_offering_id_course_offerings_id_fk", "tableFrom": "grades", "tableTo": "course_offerings", "columnsFrom": ["course_offering_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "orders": {"name": "orders", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "ordination_id": {"name": "ordination_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "seminarian_id": {"name": "seminarian_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_name": {"name": "order_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "titulum": {"name": "titulum", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"orders_ordination_id_ordinations_id_fk": {"name": "orders_ordination_id_ordinations_id_fk", "tableFrom": "orders", "tableTo": "ordinations", "columnsFrom": ["ordination_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "orders_seminarian_id_seminarians_id_fk": {"name": "orders_seminarian_id_seminarians_id_fk", "tableFrom": "orders", "tableTo": "seminarians", "columnsFrom": ["seminarian_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "ordinations": {"name": "ordinations", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "date": {"name": "date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "bishop": {"name": "bishop", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "place_name": {"name": "place_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "place": {"name": "place", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "room_assignments": {"name": "room_assignments", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "seminarian_id": {"name": "seminarian_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "room_number": {"name": "room_number", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "assigned_date": {"name": "assigned_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "vacated_date": {"name": "vacated_date", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"room_assignments_seminarian_id_seminarians_id_fk": {"name": "room_assignments_seminarian_id_seminarians_id_fk", "tableFrom": "room_assignments", "tableTo": "seminarians", "columnsFrom": ["seminarian_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "seminarians": {"name": "seminarians", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "middle_name": {"name": "middle_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "street1": {"name": "street1", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "street2": {"name": "street2", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "state": {"name": "state", "type": "text(2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "zip": {"name": "zip", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "mailing_street": {"name": "mailing_street", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "citizenship": {"name": "citizenship", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "diocese": {"name": "diocese", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "dob": {"name": "dob", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "birth_city": {"name": "birth_city", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "birth_state": {"name": "birth_state", "type": "text(2)", "primaryKey": false, "notNull": false, "autoincrement": false}, "birth_country": {"name": "birth_country", "type": "text(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'Applicant'"}, "seminary_year": {"name": "seminary_year", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "semester": {"name": "semester", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "room_number": {"name": "room_number", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"seminarians_email_unique": {"name": "seminarians_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "status_updates": {"name": "status_updates", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "seminarian_id": {"name": "seminarian_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "update_date": {"name": "update_date", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"status_updates_seminarian_id_seminarians_id_fk": {"name": "status_updates_seminarian_id_seminarians_id_fk", "tableFrom": "status_updates", "tableTo": "seminarians", "columnsFrom": ["seminarian_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_roles": {"name": "user_roles", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_roles_user_id_role_pk": {"columns": ["user_id", "role"], "name": "user_roles_user_id_role_pk"}}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "honorific": {"name": "honorific", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "associated_seminarian_id": {"name": "associated_seminarian_id", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "password_hash": {"name": "password_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}}, "indexes": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"], "isUnique": true}}, "foreignKeys": {"users_associated_seminarian_id_seminarians_id_fk": {"name": "users_associated_seminarian_id_seminarians_id_fk", "tableFrom": "users", "tableTo": "seminarians", "columnsFrom": ["associated_seminarian_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}
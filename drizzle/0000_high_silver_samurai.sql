CREATE TABLE `applications` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`seminarian_id` integer,
	`application_entered` integer,
	`acceptance_sent` integer,
	`baptismal_certificate` integer DEFAULT false,
	`confirmation_certificate` integer DEFAULT false,
	`catholic_marriage` integer DEFAULT false,
	`marriage_certificate` integer DEFAULT false,
	`academic_transcripts` integer DEFAULT false,
	`reference_letter` integer DEFAULT false,
	`reference_letter_from` text,
	`application_notes` text,
	`sspx_school` text,
	`perpetual_engagement` integer,
	`skills` text,
	`musical_ability` text,
	`notes` text,
	FOREIGN KEY (`seminarian_id`) REFERENCES `seminarians`(`id`) ON UPDATE no action ON DELETE set null
);
--> statement-breakpoint
CREATE TABLE `course_enrollments` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`seminarian_id` integer NOT NULL,
	`course_offering_id` integer NOT NULL,
	`credits_earned` integer,
	`percent_grade` integer,
	`notes` text,
	FOREIGN KEY (`seminarian_id`) REFERENCES `seminarians`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`course_offering_id`) REFERENCES `course_offerings`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `course_enrollments_seminarian_id_course_offering_id_unique` ON `course_enrollments` (`seminarian_id`,`course_offering_id`);--> statement-breakpoint
CREATE TABLE `course_offerings` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`course_id` integer NOT NULL,
	`class_room` text,
	`academic_year` integer NOT NULL,
	`semester` text NOT NULL,
	FOREIGN KEY (`course_id`) REFERENCES `courses`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE UNIQUE INDEX `course_offerings_course_id_academic_year_semester_unique` ON `course_offerings` (`course_id`,`academic_year`,`semester`);--> statement-breakpoint
CREATE TABLE `courses` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`course_name` text NOT NULL,
	`credits` integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE `department_assignments` (
	`seminarian_id` integer NOT NULL,
	`department_id` integer NOT NULL,
	PRIMARY KEY(`seminarian_id`, `department_id`),
	FOREIGN KEY (`seminarian_id`) REFERENCES `seminarians`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`department_id`) REFERENCES `departments`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `departments` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`category` text,
	`sub_prefect_seminarian_id` integer,
	FOREIGN KEY (`sub_prefect_seminarian_id`) REFERENCES `seminarians`(`id`) ON UPDATE no action ON DELETE set null
);
--> statement-breakpoint
CREATE TABLE `orders` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`ordination_id` integer,
	`seminarian_id` integer,
	`order_name` text,
	`titulum` text,
	FOREIGN KEY (`ordination_id`) REFERENCES `ordinations`(`id`) ON UPDATE no action ON DELETE cascade,
	FOREIGN KEY (`seminarian_id`) REFERENCES `seminarians`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `ordinations` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`date` integer,
	`bishop` text,
	`place_name` text,
	`place` text,
	`notes` text
);
--> statement-breakpoint
CREATE TABLE `seminarians` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`last_name` text NOT NULL,
	`first_name` text,
	`middle_name` text,
	`street1` text,
	`street2` text,
	`city` text,
	`state` text(2),
	`zip` text,
	`country` text(3),
	`mailing_street` text,
	`phone` text,
	`email` text,
	`citizenship` text,
	`diocese` text,
	`dob` integer,
	`birth_city` text,
	`birth_state` text(2),
	`birth_country` text(3),
	`status` text DEFAULT 'Applicant' NOT NULL,
	`seminary_year` text,
	`class_year` integer,
	`room_number` integer,
	`notes` text
);
--> statement-breakpoint
CREATE UNIQUE INDEX `seminarians_email_unique` ON `seminarians` (`email`);--> statement-breakpoint
CREATE TABLE `user_activity` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`user_id` integer NOT NULL,
	`relationship` text NOT NULL,
	`old` text,
	`new` text,
	`action` text NOT NULL,
	`resource_type` text,
	`resource_id` text,
	`note` text,
	`timestamp` integer NOT NULL,
	FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON UPDATE no action ON DELETE cascade
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`email` text,
	`phone` text,
	`honorific` text,
	`first_name` text,
	`last_name` text,
	`username` text NOT NULL,
	`password_hash` text NOT NULL,
	`is_active` integer DEFAULT true NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_username_unique` ON `users` (`username`);
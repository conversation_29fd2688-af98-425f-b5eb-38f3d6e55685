import { createRe<PERSON><PERSON><PERSON><PERSON> } from "@react-router/express";
import { drizzle } from "drizzle-orm/better-sqlite3";
import Database from 'better-sqlite3';
import express from "express";
import "react-router";
import type { unstable_InitialContext, unstable_RouterContext } from "react-router";

import { DatabaseContext } from "~/database/context";
import * as schema from "~/database/schema";

export const app = express();

const client = new Database(process.env.DATABASE_FILE!);
const db = drizzle(client, { schema });
app.use((_, __, next) => DatabaseContext.run(db, next));

app.use(
  createRequestHandler({
    build: () => import("virtual:react-router/server-build"),
    getLoadContext: (req, res): unstable_InitialContext => {
      const map = new Map<unstable_RouterContext<unknown>, unknown>();
      return map;
    },
  })
);
